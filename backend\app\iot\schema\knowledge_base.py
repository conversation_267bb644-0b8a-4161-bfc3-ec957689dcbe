#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库相关的 Schema 定义

基于 RAGFlow API 规范设计的知识库数据模型
简化版本：只保留核心的请求验证 Schema，移除未使用的响应模型
"""
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ParserConfig(BaseModel):
    """解析器配置 - 与前端配置保持一致"""
    chunk_token_num: int = Field(default=128, ge=1, le=2048, description="分块token数量")
    delimiter: str = Field(default="\\n!?;。；！？", description="分隔符")
    html4excel: bool = Field(default=False, description="是否将Excel转换为HTML格式")
    layout_recognize: str = Field(default="DeepDOC", description="布局识别方法")
    auto_keywords: int = Field(default=0, ge=0, le=32, description="自动关键词数量")
    auto_questions: int = Field(default=0, ge=0, le=10, description="自动问题数量")
    task_page_size: int = Field(default=12, ge=1, description="PDF任务页面大小")
    tag_kb_ids: Optional[List[str]] = Field(default=None, description="标签知识库ID列表")
    raptor: Dict[str, Any] = Field(default_factory=lambda: {"use_raptor": False}, description="RAPTOR设置")
    graphrag: Dict[str, Any] = Field(default_factory=lambda: {"use_graphrag": False}, description="GRAPHRAG设置")


class KnowledgeBaseCreate(BaseModel):
    """创建知识库请求"""
    name: str = Field(..., max_length=128, description="知识库名称")
    avatar: Optional[str] = Field(None, max_length=65535, description="头像Base64编码")
    description: Optional[str] = Field(None, max_length=65535, description="知识库描述")
    embedding_model: str = Field(
        default="Ollama/bge-m3:latest@Ollama",
        max_length=255,
        description="嵌入模型名称"
    )
    permission: str = Field(default="me", description="访问权限 (me/team)")
    chunk_method: str = Field(
        default="naive",
        description="分块方法 (naive/book/email/laws/manual/one/paper/picture/presentation/qa/table/tag)"
    )
    pagerank: int = Field(default=0, ge=0, le=100, description="页面排名")
    parser_config: Optional[ParserConfig] = Field(default_factory=ParserConfig, description="解析器配置")


class KnowledgeBaseUpdate(BaseModel):
    """更新知识库请求"""
    name: Optional[str] = Field(None, max_length=128, description="知识库名称")
    avatar: Optional[str] = Field(None, max_length=65535, description="头像Base64编码")
    description: Optional[str] = Field(None, max_length=65535, description="知识库描述")
    embedding_model: Optional[str] = Field(None, max_length=255, description="嵌入模型名称")
    permission: Optional[str] = Field(None, description="访问权限 (me/team)")
    chunk_method: Optional[str] = Field(None, description="分块方法")
    pagerank: Optional[int] = Field(None, ge=0, le=100, description="页面排名")
    parser_config: Optional[ParserConfig] = Field(None, description="解析器配置")


# 注意：响应模型类已移除，直接使用 RAGFlow 返回的数据格式
# 这样可以保持数据的原真性，避免不必要的数据转换


class KnowledgeBaseDelete(BaseModel):
    """删除知识库请求"""
    ids: Optional[List[str]] = Field(None, description="要删除的知识库ID列表，null表示删除所有")


class KnowledgeBaseQuery(BaseModel):
    """知识库查询参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=30, ge=1, le=100, description="每页大小")
    orderby: str = Field(default="create_time", description="排序字段 (create_time/update_time)")
    desc: bool = Field(default=True, description="是否降序排列")
    name: Optional[str] = Field(None, description="知识库名称过滤")
    id: Optional[str] = Field(None, description="知识库ID过滤")


# 注意：统计信息、响应模型和错误码类已移除
# 原因：
# 1. 统计信息通过专门的统计接口处理，不需要单独的 Schema
# 2. 响应模型统一使用 backend.common.response.response_schema.ResponseModel
# 3. 错误码使用项目统一的错误处理机制，前端有自己的错误码映射
# 4. 保持代码简洁，避免维护未使用的代码
