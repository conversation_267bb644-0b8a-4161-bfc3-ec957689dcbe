<template>
  <div class="document-parse-status-container">
    <!-- 解析状态概览 -->
    <div class="status-overview">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>解析状态概览</span>
            <el-button
              type="text"
              :icon="Refresh"
              @click="refreshStatus"
              :loading="refreshing"
            >
              刷新
            </el-button>
          </div>
        </template>
        
        <div class="status-stats">
          <div class="stat-item">
            <div class="stat-value">{{ statusStats.total }}</div>
            <div class="stat-label">总文档数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value success">{{ statusStats.parsed }}</div>
            <div class="stat-label">已解析</div>
          </div>
          <div class="stat-item">
            <div class="stat-value warning">{{ statusStats.parsing }}</div>
            <div class="stat-label">解析中</div>
          </div>
          <div class="stat-item">
            <div class="stat-value info">{{ statusStats.uploaded }}</div>
            <div class="stat-label">待解析</div>
          </div>
          <div class="stat-item">
            <div class="stat-value danger">{{ statusStats.failed }}</div>
            <div class="stat-label">解析失败</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 解析进度列表 -->
    <div class="parse-progress-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>解析进度</span>
            <div class="header-actions">
              <el-button
                type="primary"
                :icon="VideoPlay"
                @click="startBatchParsing"
                :disabled="!canStartBatch"
                :loading="batchStarting"
              >
                批量解析
              </el-button>
              <el-button
                type="warning"
                :icon="VideoPause"
                @click="stopBatchParsing"
                :disabled="!canStopBatch"
                :loading="batchStopping"
              >
                停止全部
              </el-button>
            </div>
          </div>
        </template>
        
        <!-- 筛选器 -->
        <div class="filter-bar">
          <el-select
            v-model="statusFilter"
            placeholder="状态筛选"
            clearable
            @change="handleFilterChange"
            style="width: 150px"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
          
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文档名称"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            style="width: 250px"
          />
        </div>
        
        <!-- 进度列表 -->
        <div class="progress-list">
          <div
            v-for="doc in filteredDocuments"
            :key="doc.id"
            class="progress-item"
            :class="{ 'parsing': doc.status === 'parsing' }"
          >
            <div class="document-info">
              <div class="document-icon">
                <el-icon :size="20" :color="getFileIconColor(doc.type || '')">
                  <Document />
                </el-icon>
              </div>
              <div class="document-details">
                <div class="document-name">{{ doc.name }}</div>
                <div class="document-meta">
                  {{ formatFileSize(doc.size || 0) }} • {{ getFileTypeLabel(doc.type || '') }}
                </div>
              </div>
            </div>
            
            <div class="parse-info">
              <div class="status-section">
                <el-tag
                  :type="getStatusTagType(doc.status || '')"
                  size="small"
                >
                  {{ getStatusLabel(doc.status || '') }}
                </el-tag>
                
                <div v-if="doc.status === 'parsing'" class="progress-section">
                  <el-progress
                    :percentage="Math.round((doc.progress || 0) * 100)"
                    :stroke-width="6"
                    :show-text="false"
                  />
                  <span class="progress-text">
                    {{ Math.round((doc.progress || 0) * 100) }}%
                  </span>
                </div>
                
                <div v-else-if="doc.status === 'parsed'" class="result-section">
                  <div class="result-item">
                    <el-icon><Grid /></el-icon>
                    <span>{{ doc.chunk_num || 0 }} 分块</span>
                  </div>
                  <div class="result-item">
                    <el-icon><Coin /></el-icon>
                    <span>{{ formatNumber(doc.token_num || 0) }} Token</span>
                  </div>
                </div>
                
                <div v-else-if="doc.status === 'failed'" class="error-section">
                  <el-tooltip :content="doc.error_msg || '解析失败'" placement="top">
                    <el-icon color="#F56C6C"><Warning /></el-icon>
                  </el-tooltip>
                  <span class="error-text">{{ doc.error_msg || '解析失败' }}</span>
                </div>
              </div>
              
              <div class="time-section">
                <div class="time-item">
                  <span class="time-label">创建:</span>
                  <span class="time-value">{{ formatDateTime(doc.create_time || '') }}</span>
                </div>
                <div v-if="doc.update_time" class="time-item">
                  <span class="time-label">更新:</span>
                  <span class="time-value">{{ formatDateTime(doc.update_time) }}</span>
                </div>
              </div>
            </div>
            
            <div class="action-section">
              <el-button
                v-if="doc.status === 'uploaded' || doc.status === 'failed'"
                type="primary"
                size="small"
                :icon="VideoPlay"
                @click="startParsing(doc)"
                :loading="doc.starting"
              >
                开始解析
              </el-button>
              
              <el-button
                v-if="doc.status === 'parsing'"
                type="warning"
                size="small"
                :icon="VideoPause"
                @click="stopParsing(doc)"
                :loading="doc.stopping"
              >
                停止解析
              </el-button>
              
              <el-button
                v-if="doc.status === 'parsed'"
                type="success"
                size="small"
                :icon="View"
                @click="viewResult(doc)"
              >
                查看结果
              </el-button>
              
              <el-dropdown
                v-if="doc.status !== 'parsing'"
                @command="(command: string) => handleAction(command, doc)"
              >
                <el-button type="text" size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="reparse">重新解析</el-dropdown-item>
                    <el-dropdown-item command="config">解析配置</el-dropdown-item>
                    <el-dropdown-item command="log">查看日志</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="filteredDocuments.length === 0" class="empty-state">
            <el-empty description="暂无文档数据" />
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 解析配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="解析配置"
      width="600px"
    >
      <el-form :model="parseConfig" label-width="120px">
        <el-form-item label="解析器类型">
          <el-select v-model="parseConfig.parser_id" style="width: 100%">
            <el-option
              v-for="parser in parserOptions"
              :key="parser.value"
              :label="parser.label"
              :value="parser.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分块大小">
          <el-input-number
            v-model="parseConfig.chunk_token_count"
            :min="64"
            :max="1024"
            :step="64"
          />
          <span class="form-hint">Token数量，建议128-512</span>
        </el-form-item>
        
        <el-form-item label="布局识别">
          <el-switch v-model="parseConfig.layout_recognize" />
          <span class="form-hint">是否识别文档布局结构</span>
        </el-form-item>
        
        <el-form-item label="分隔符">
          <el-input
            v-model="parseConfig.delimiter"
            placeholder="\\n!?。；！？"
          />
          <span class="form-hint">用于分割文本的分隔符</span>
        </el-form-item>
        
        <el-form-item label="页面大小">
          <el-input-number
            v-model="parseConfig.task_page_size"
            :min="1"
            :max="50"
          />
          <span class="form-hint">每次处理的页面数量</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="configSaving">
          保存并解析
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 解析日志对话框 -->
    <el-dialog
      v-model="logDialogVisible"
      title="解析日志"
      width="80%"
    >
      <div class="log-container">
        <div class="log-header">
          <el-button :icon="Refresh" @click="refreshLog" :loading="logLoading">
            刷新日志
          </el-button>
          <el-button :icon="Download" @click="downloadLog">
            下载日志
          </el-button>
        </div>
        
        <div class="log-content">
          <pre v-if="parseLog" class="log-text">{{ parseLog }}</pre>
          <el-empty v-else description="暂无日志数据" />
        </div>
      </div>
    </el-dialog>
    
    <!-- 解析结果对话框 -->
    <el-dialog
      v-model="resultDialogVisible"
      title="解析结果"
      width="80%"
    >
      <div class="result-container">
        <div class="result-summary">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="分块数量">
              {{ currentResult?.chunk_num || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="Token数量">
              {{ formatNumber(currentResult?.token_num || 0) }}
            </el-descriptions-item>
            <el-descriptions-item label="解析时长">
              {{ calculateDuration(currentResult) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="result-chunks">
          <h4>文档分块预览</h4>
          <div class="chunk-list">
            <div
              v-for="(chunk, index) in resultChunks"
              :key="index"
              class="chunk-item"
            >
              <div class="chunk-header">
                <span class="chunk-index">分块 {{ index + 1 }}</span>
                <span class="chunk-tokens">{{ chunk.token_count }} tokens</span>
              </div>
              <div class="chunk-content">
                {{ chunk.content }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Refresh,
  VideoPlay,
  VideoPause,
  Search,
  Document,
  Grid,
  Coin,
  Warning,
  View,
  Download,
  ArrowDown
} from '@element-plus/icons-vue';

import {
  getDocumentList,
  startDocumentParsing,
  stopDocumentParsing,
  getParserOptions,
  formatFileSize,
  type DocumentInfo
} from '/@/api/iot/document';

// Props
interface Props {
  knowledgeBaseId: string;
  documents?: DocumentInfo[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  statusChange: [documents: DocumentInfo[]];
  parseComplete: [document: DocumentInfo];
  parseError: [document: DocumentInfo, error: string];
}>();

// 响应式数据
const refreshing = ref(false);
const documents = ref<DocumentInfo[]>([]);
const statusFilter = ref('');
const searchKeyword = ref('');

// 批量操作
const batchStarting = ref(false);
const batchStopping = ref(false);

// 对话框状态
const configDialogVisible = ref(false);
const logDialogVisible = ref(false);
const resultDialogVisible = ref(false);
const configSaving = ref(false);
const logLoading = ref(false);

// 当前操作的文档
const currentDoc = ref<DocumentInfo | null>(null);
const currentResult = ref<DocumentInfo | null>(null);

// 解析配置
const parseConfig = reactive({
  parser_id: 'naive',
  chunk_token_count: 128,
  layout_recognize: true,
  delimiter: '\\n!?。；！？',
  task_page_size: 12
});

// 解析日志和结果
const parseLog = ref('');
const resultChunks = ref<any[]>([]);

// 选项数据
const statusOptions = [
  { label: '已上传', value: 'uploaded' },
  { label: '解析中', value: 'parsing' },
  { label: '已解析', value: 'parsed' },
  { label: '失败', value: 'failed' }
];

const parserOptions = getParserOptions();

// 计算属性
const statusStats = computed(() => {
  const stats = {
    total: documents.value.length,
    uploaded: 0,
    parsing: 0,
    parsed: 0,
    failed: 0
  };
  
  documents.value.forEach(doc => {
    if (doc.status) {
      stats[doc.status as keyof typeof stats]++;
    }
  });
  
  return stats;
});

const filteredDocuments = computed(() => {
  let filtered = documents.value;
  
  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(doc => doc.status === statusFilter.value);
  }
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(doc => 
      doc.name.toLowerCase().includes(keyword)
    );
  }
  
  return filtered;
});

const canStartBatch = computed(() => {
  return documents.value.some(doc => 
    doc.status === 'uploaded' || doc.status === 'failed'
  );
});

const canStopBatch = computed(() => {
  return documents.value.some(doc => doc.status === 'parsing');
});

// 方法
const loadDocuments = async () => {
  if (!props.knowledgeBaseId) return;
  
  try {
    const response = await getDocumentList({
      kb_id: props.knowledgeBaseId,
      page: 1,
      page_size: 100 // 获取文档列表（受后端限制）
    });

    const businessData = response.data;

    if (businessData.code === 200) {
      // 转换RAGFlow数据格式到前端期望格式
      const rawDocs = businessData.data?.docs || [];
      documents.value = rawDocs.map((doc: any) => ({
        ...doc,
        chunk_num: doc.chunk_count,
        token_num: doc.token_count,
        status: mapRAGFlowStatus(doc.run, doc.status),
        kb_id: props.knowledgeBaseId // 添加知识库ID
      }));
      emit('statusChange', documents.value);
    }
  } catch (error) {
    console.error('Load documents error:', error);
  }
};

const refreshStatus = async () => {
  refreshing.value = true;
  try {
    await loadDocuments();
  } finally {
    refreshing.value = false;
  }
};



const handleFilterChange = () => {
  // 筛选变化处理
};

const handleSearch = () => {
  // 搜索处理
};

// 解析操作
const startParsing = async (doc: DocumentInfo) => {
  if (!doc.id) return;
  
  doc.starting = true;
  try {
    const response = await startDocumentParsing({
      kb_id: props.knowledgeBaseId,
      doc_id: doc.id,
      parser_id: doc.parser_id || 'naive'
    });
    
    if (response.code === 200) {
      ElMessage.success(`开始解析文档: ${doc.name}`);
      doc.status = 'parsing';
      doc.progress = 0;
      await loadDocuments();
    } else {
      ElMessage.error(response.message || '开始解析失败');
    }
  } catch (error) {
    ElMessage.error('开始解析失败');
    emit('parseError', doc, error instanceof Error ? error.message : '解析失败');
  } finally {
    doc.starting = false;
  }
};

const stopParsing = async (doc: DocumentInfo) => {
  if (!doc.id) return;
  
  doc.stopping = true;
  try {
    const response = await stopDocumentParsing(props.knowledgeBaseId, doc.id);
    
    if (response.code === 200) {
      ElMessage.success(`停止解析文档: ${doc.name}`);
      doc.status = 'cancelled';
      await loadDocuments();
    } else {
      ElMessage.error(response.message || '停止解析失败');
    }
  } catch (error) {
    ElMessage.error('停止解析失败');
  } finally {
    doc.stopping = false;
  }
};

const startBatchParsing = async () => {
  const pendingDocs = documents.value.filter(doc => 
    doc.status === 'uploaded' || doc.status === 'failed'
  );
  
  if (pendingDocs.length === 0) {
    ElMessage.warning('没有可解析的文档');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要开始解析 ${pendingDocs.length} 个文档吗？`,
      '批量解析确认',
      {
        confirmButtonText: '开始解析',
        cancelButtonText: '取消',
        type: 'info'
      }
    );
    
    batchStarting.value = true;
    
    for (const doc of pendingDocs) {
      await startParsing(doc);
    }
    
    ElMessage.success(`开始批量解析 ${pendingDocs.length} 个文档`);
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量解析失败');
    }
  } finally {
    batchStarting.value = false;
  }
};

const stopBatchParsing = async () => {
  const parsingDocs = documents.value.filter(doc => doc.status === 'parsing');
  
  if (parsingDocs.length === 0) {
    ElMessage.warning('没有正在解析的文档');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要停止 ${parsingDocs.length} 个正在解析的文档吗？`,
      '停止解析确认',
      {
        confirmButtonText: '停止解析',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    batchStopping.value = true;
    
    for (const doc of parsingDocs) {
      await stopParsing(doc);
    }
    
    ElMessage.success(`停止 ${parsingDocs.length} 个文档的解析`);
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量停止失败');
    }
  } finally {
    batchStopping.value = false;
  }
};

const handleAction = (command: string, doc: DocumentInfo) => {
  currentDoc.value = doc;
  
  switch (command) {
    case 'reparse':
      startParsing(doc);
      break;
    case 'config':
      openConfigDialog(doc);
      break;
    case 'log':
      openLogDialog(doc);
      break;
    case 'delete':
      // 删除文档逻辑
      break;
  }
};

const openConfigDialog = (doc: DocumentInfo) => {
  currentDoc.value = doc;
  parseConfig.parser_id = doc.parser_id || 'naive';
  configDialogVisible.value = true;
};

const saveConfig = async () => {
  if (!currentDoc.value?.id) return;
  
  configSaving.value = true;
  try {
    const response = await startDocumentParsing({
      kb_id: props.knowledgeBaseId,
      doc_id: currentDoc.value.id,
      parser_id: parseConfig.parser_id,
      parser_config: {
        chunk_token_count: parseConfig.chunk_token_count,
        layout_recognize: parseConfig.layout_recognize,
        delimiter: parseConfig.delimiter,
        task_page_size: parseConfig.task_page_size
      }
    });
    
    if (response.code === 200) {
      ElMessage.success('保存配置并开始解析');
      configDialogVisible.value = false;
      await loadDocuments();
    } else {
      ElMessage.error(response.message || '保存配置失败');
    }
  } catch (error) {
    ElMessage.error('保存配置失败');
  } finally {
    configSaving.value = false;
  }
};

const openLogDialog = (doc: DocumentInfo) => {
  currentDoc.value = doc;
  logDialogVisible.value = true;
  refreshLog();
};

const refreshLog = async () => {
  if (!currentDoc.value?.id) return;
  
  logLoading.value = true;
  try {
    // 模拟获取解析日志
    parseLog.value = `解析日志 - ${currentDoc.value.name}\n\n` +
      `开始时间: ${new Date().toLocaleString()}\n` +
      `解析器: ${currentDoc.value.parser_id}\n` +
      `状态: ${currentDoc.value.status}\n` +
      `进度: ${Math.round((currentDoc.value.progress || 0) * 100)}%\n\n` +
      `详细日志:\n` +
      `[INFO] 开始解析文档\n` +
      `[INFO] 文档类型检测完成\n` +
      `[INFO] 开始文本提取\n` +
      `[INFO] 文本分块处理\n` +
      `[INFO] 向量化处理\n` +
      `[SUCCESS] 解析完成`;
  } catch (error) {
    ElMessage.error('获取日志失败');
  } finally {
    logLoading.value = false;
  }
};

const downloadLog = () => {
  if (!parseLog.value) return;
  
  const blob = new Blob([parseLog.value], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `parse-log-${currentDoc.value?.name || 'document'}.txt`;
  link.click();
  URL.revokeObjectURL(url);
};

const viewResult = (doc: DocumentInfo) => {
  currentResult.value = doc;
  resultDialogVisible.value = true;
  
  // 模拟获取分块结果
  resultChunks.value = [
    {
      content: '这是第一个文档分块的内容，包含了文档的开头部分...',
      token_count: 128
    },
    {
      content: '这是第二个文档分块的内容，继续文档的中间部分...',
      token_count: 156
    },
    {
      content: '这是第三个文档分块的内容，包含了文档的结尾部分...',
      token_count: 98
    }
  ];
};

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    uploaded: 'info',
    parsing: 'warning',
    parsed: 'success',
    failed: 'danger',
    cancelled: 'info'
  };
  return typeMap[status] || 'info';
};

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    uploaded: '已上传',
    parsing: '解析中',
    parsed: '已解析',
    failed: '失败',
    cancelled: '已取消'
  };
  return labelMap[status] || status;
};

const getFileTypeLabel = (mimeType: string) => {
  const typeMap: Record<string, string> = {
    'application/pdf': 'PDF',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
    'text/plain': 'TXT'
  };
  return typeMap[mimeType] || '文档';
};

const getFileIconColor = (mimeType: string) => {
  if (mimeType?.includes('pdf')) return '#F56C6C';
  if (mimeType?.includes('word')) return '#409EFF';
  if (mimeType?.includes('text')) return '#909399';
  return '#409EFF';
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// RAGFlow状态映射到前端状态
const mapRAGFlowStatus = (run: string, status: string) => {
  // run字段：RUNNING, DONE, FAIL, CANCEL
  // status字段：'0'(未开始), '1'(已完成), '2'(失败)
  if (run === 'RUNNING') return 'parsing';
  if (run === 'DONE' && status === '1') return 'parsed';
  if (run === 'FAIL' || status === '2') return 'failed';
  if (run === 'CANCEL') return 'cancelled';
  return 'uploaded'; // 默认状态
};

const calculateDuration = (doc: DocumentInfo | null) => {
  if (!doc?.create_time || !doc?.update_time) return '-';
  
  const start = new Date(doc.create_time);
  const end = new Date(doc.update_time);
  const duration = end.getTime() - start.getTime();
  
  const minutes = Math.floor(duration / 60000);
  const seconds = Math.floor((duration % 60000) / 1000);
  
  return `${minutes}分${seconds}秒`;
};

// 生命周期
onMounted(() => {
  loadDocuments();
});

// 暴露方法
defineExpose({
  refreshStatus,
  startBatchParsing,
  stopBatchParsing
});
</script>

<style scoped>
.document-parse-status-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.status-stats {
  display: flex;
  gap: 24px;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-value.success { color: #67C23A; }
.stat-value.warning { color: #E6A23C; }
.stat-value.info { color: #409EFF; }
.stat-value.danger { color: #F56C6C; }

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.filter-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.progress-list {
  max-height: 600px;
  overflow-y: auto;
}

.progress-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.progress-item:hover {
  background-color: #f8f9fa;
}

.progress-item.parsing {
  background-color: #fff7e6;
  border-left: 3px solid #E6A23C;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 200px;
}

.document-details {
  flex: 1;
  min-width: 0;
}

.document-name {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-meta {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.parse-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 0 20px;
}

.status-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 30px;
}

.result-section {
  display: flex;
  gap: 16px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.error-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-text {
  font-size: 12px;
  color: #F56C6C;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.time-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-item {
  font-size: 11px;
  color: #909399;
}

.time-label {
  margin-right: 4px;
}

.action-section {
  display: flex;
  gap: 8px;
  align-items: center;
}

.empty-state {
  padding: 40px 0;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.log-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.log-header {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.log-content {
  flex: 1;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.log-text {
  padding: 16px;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  background: #f8f9fa;
  color: #303133;
  height: 100%;
  overflow: auto;
}

.result-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-summary {
  margin-bottom: 20px;
}

.chunk-list {
  max-height: 400px;
  overflow-y: auto;
}

.chunk-item {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 12px;
  overflow: hidden;
}

.chunk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.chunk-index {
  font-weight: 500;
  color: #303133;
}

.chunk-tokens {
  font-size: 12px;
  color: #909399;
}

.chunk-content {
  padding: 12px;
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  max-height: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .status-stats {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .progress-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .parse-info {
    margin: 0;
  }
  
  .filter-bar {
    flex-direction: column;
  }
}
</style>
