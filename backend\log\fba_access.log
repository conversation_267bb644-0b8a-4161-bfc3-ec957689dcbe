2025-08-15 09:26:17.063 | INFO     | 637941ffede94486b438e37ad23bc2f6 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:26:17.073 | INFO     | 637941ffede94486b438e37ad23bc2f6 | 成功认证Java用户: pythontest
2025-08-15 09:26:17.112 | INFO     | 637941ffede94486b438e37ad23bc2f6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:26:17.113 | INFO     | 637941ffede94486b438e37ad23bc2f6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:26:17.220 | INFO     | 637941ffede94486b438e37ad23bc2f6 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:26:17.222 | INFO     | 637941ffede94486b438e37ad23bc2f6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 162.855ms
2025-08-15 09:26:17.290 | INFO     | 481941f6783d48adb5a4596a8b8db891 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:26:17.291 | INFO     | 481941f6783d48adb5a4596a8b8db891 | 成功认证Java用户: pythontest
2025-08-15 09:26:17.315 | INFO     | 481941f6783d48adb5a4596a8b8db891 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:26:17.316 | INFO     | 481941f6783d48adb5a4596a8b8db891 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:26:17.341 | INFO     | 481941f6783d48adb5a4596a8b8db891 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:26:17.343 | INFO     | 481941f6783d48adb5a4596a8b8db891 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/list/page=1&page_size=20&orderby=create_time&desc=true | 53.281ms
2025-08-15 09:26:19.212 | INFO     | 5078f7d881f44783813da1d576f1d220 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:26:19.214 | INFO     | 5078f7d881f44783813da1d576f1d220 | 成功认证Java用户: pythontest
2025-08-15 09:26:19.220 | INFO     | 5078f7d881f44783813da1d576f1d220 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:26:19.221 | INFO     | 5078f7d881f44783813da1d576f1d220 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:26:19.526 | INFO     | 5078f7d881f44783813da1d576f1d220 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/a5f2f8ac78de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:26:19.600 | INFO     | 5078f7d881f44783813da1d576f1d220 | 文档预览调试 - doc_id: a5f2f8ac78de11f080f07e63526e5b16
2025-08-15 09:26:19.601 | INFO     | 5078f7d881f44783813da1d576f1d220 | doc_name: 'test.xlsx', doc_data keys: ['content', 'content_type', 'status_code']
2025-08-15 09:26:19.601 | INFO     | 5078f7d881f44783813da1d576f1d220 | RAGFlow返回的文档信息: {'content_type': 'application/octet-stream', 'status_code': 200}
2025-08-15 09:26:19.601 | INFO     | 5078f7d881f44783813da1d576f1d220 | 根据文件头检测到Office文档格式(ZIP)，修正类型为office
2025-08-15 09:26:19.601 | INFO     | 5078f7d881f44783813da1d576f1d220 | 最终识别的文档类型: office
2025-08-15 09:26:19.602 | INFO     | 5078f7d881f44783813da1d576f1d220 | 检测到Office文档: test.xlsx，返回原始文档URL用于vue-office预览
2025-08-15 09:26:19.603 | INFO     | 5078f7d881f44783813da1d576f1d220 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/a5f2f8ac78de11f080f07e63526e5b16/preview/doc_name=test.xlsx | 390.236ms
2025-08-15 09:26:19.610 | INFO     | ed86adbead5b458b97930bb749ed11e4 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:26:19.611 | INFO     | ed86adbead5b458b97930bb749ed11e4 | 成功认证Java用户: pythontest
2025-08-15 09:26:19.617 | INFO     | ed86adbead5b458b97930bb749ed11e4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:26:19.618 | INFO     | ed86adbead5b458b97930bb749ed11e4 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:26:19.650 | INFO     | ed86adbead5b458b97930bb749ed11e4 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/a5f2f8ac78de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:26:19.754 | INFO     | ed86adbead5b458b97930bb749ed11e4 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents/a5f2f8ac78de11f080f07e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:26:19.827 | INFO     | ed86adbead5b458b97930bb749ed11e4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/a5f2f8ac78de11f080f07e63526e5b16/content | 218.021ms
2025-08-15 09:27:29.127 | INFO     | 256776e9eeef4bc18e6f6d82d5ab3749 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:27:29.129 | INFO     | 256776e9eeef4bc18e6f6d82d5ab3749 | 成功认证Java用户: pythontest
2025-08-15 09:27:29.148 | INFO     | 256776e9eeef4bc18e6f6d82d5ab3749 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:27:29.150 | INFO     | 256776e9eeef4bc18e6f6d82d5ab3749 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.414ms
2025-08-15 09:27:29.152 | INFO     | a9bb28cd278d45ed876c0b34fa026c4e | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:27:29.153 | INFO     | a9bb28cd278d45ed876c0b34fa026c4e | 成功认证Java用户: pythontest
2025-08-15 09:27:29.160 | INFO     | a9bb28cd278d45ed876c0b34fa026c4e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:27:29.160 | INFO     | a9bb28cd278d45ed876c0b34fa026c4e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:27:29.178 | INFO     | a9bb28cd278d45ed876c0b34fa026c4e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:27:29.180 | INFO     | a9bb28cd278d45ed876c0b34fa026c4e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.462ms
2025-08-15 09:27:29.184 | INFO     | c0d7c7820b444af689ae6ba9a309d04a | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:27:29.185 | INFO     | c0d7c7820b444af689ae6ba9a309d04a | 成功认证Java用户: pythontest
2025-08-15 09:27:29.189 | INFO     | c0d7c7820b444af689ae6ba9a309d04a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:27:29.189 | INFO     | c0d7c7820b444af689ae6ba9a309d04a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:27:29.203 | INFO     | c0d7c7820b444af689ae6ba9a309d04a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:27:29.205 | INFO     | c0d7c7820b444af689ae6ba9a309d04a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 22.069ms
2025-08-15 09:35:21.438 | INFO     | fe46578623294e368d3964a624b6ad90 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:35:21.439 | INFO     | 6962a674756e40f6b6fd7290187f51db | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:35:21.439 | INFO     | 44ea331659014db0b66d36b12f703ab8 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:35:21.440 | INFO     | fe46578623294e368d3964a624b6ad90 | 成功认证Java用户: pythontest
2025-08-15 09:35:21.441 | INFO     | 6962a674756e40f6b6fd7290187f51db | 成功认证Java用户: pythontest
2025-08-15 09:35:21.442 | INFO     | 44ea331659014db0b66d36b12f703ab8 | 成功认证Java用户: pythontest
2025-08-15 09:35:21.489 | INFO     | 44ea331659014db0b66d36b12f703ab8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:35:21.490 | INFO     | 44ea331659014db0b66d36b12f703ab8 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:35:21.492 | INFO     | 6962a674756e40f6b6fd7290187f51db | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:35:21.492 | INFO     | 6962a674756e40f6b6fd7290187f51db | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:35:21.551 | INFO     | fe46578623294e368d3964a624b6ad90 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:35:21.552 | INFO     | 6962a674756e40f6b6fd7290187f51db | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:35:21.555 | INFO     | 44ea331659014db0b66d36b12f703ab8 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:35:21.556 | INFO     | fe46578623294e368d3964a624b6ad90 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 119.236ms
2025-08-15 09:35:21.557 | INFO     | 6962a674756e40f6b6fd7290187f51db | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 119.177ms
2025-08-15 09:35:21.558 | INFO     | 44ea331659014db0b66d36b12f703ab8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 120.565ms
2025-08-15 09:35:49.187 | INFO     | 4ae02e559a61419eae48829a4744d65b | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:35:49.188 | INFO     | 4ae02e559a61419eae48829a4744d65b | 成功认证Java用户: pythontest
2025-08-15 09:35:49.204 | INFO     | 4ae02e559a61419eae48829a4744d65b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:35:49.205 | INFO     | 4ae02e559a61419eae48829a4744d65b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.101ms
2025-08-15 09:35:49.207 | INFO     | c8117b4a22cf4972bc31fc54663f5f76 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:35:49.208 | INFO     | c8117b4a22cf4972bc31fc54663f5f76 | 成功认证Java用户: pythontest
2025-08-15 09:35:49.213 | INFO     | c8117b4a22cf4972bc31fc54663f5f76 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:35:49.213 | INFO     | c8117b4a22cf4972bc31fc54663f5f76 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:35:49.228 | INFO     | c8117b4a22cf4972bc31fc54663f5f76 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:35:49.230 | INFO     | c8117b4a22cf4972bc31fc54663f5f76 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.539ms
2025-08-15 09:35:49.231 | INFO     | 20561be375c440a492ac97428cee88bb | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:35:49.232 | INFO     | 20561be375c440a492ac97428cee88bb | 成功认证Java用户: pythontest
2025-08-15 09:35:49.238 | INFO     | 20561be375c440a492ac97428cee88bb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:35:49.239 | INFO     | 20561be375c440a492ac97428cee88bb | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:35:49.254 | INFO     | 20561be375c440a492ac97428cee88bb | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:35:49.256 | INFO     | 20561be375c440a492ac97428cee88bb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.148ms
2025-08-15 09:35:49.258 | INFO     | 46071dd037ad400aaae61b1b723c0960 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:35:49.259 | INFO     | 46071dd037ad400aaae61b1b723c0960 | 成功认证Java用户: pythontest
2025-08-15 09:35:49.273 | INFO     | 46071dd037ad400aaae61b1b723c0960 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:35:49.274 | INFO     | 46071dd037ad400aaae61b1b723c0960 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.382ms
2025-08-15 09:35:49.276 | INFO     | 11ff36bbed0947c7a88dd99095e15ac3 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:35:49.277 | INFO     | 11ff36bbed0947c7a88dd99095e15ac3 | 成功认证Java用户: pythontest
2025-08-15 09:35:49.282 | INFO     | 11ff36bbed0947c7a88dd99095e15ac3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:35:49.283 | INFO     | 11ff36bbed0947c7a88dd99095e15ac3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:35:49.298 | INFO     | 11ff36bbed0947c7a88dd99095e15ac3 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:35:49.300 | INFO     | 11ff36bbed0947c7a88dd99095e15ac3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 23.022ms
2025-08-15 09:35:49.302 | INFO     | 51784341b755433e9f01472859a683b5 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:35:49.303 | INFO     | 51784341b755433e9f01472859a683b5 | 成功认证Java用户: pythontest
2025-08-15 09:35:49.309 | INFO     | 51784341b755433e9f01472859a683b5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:35:49.309 | INFO     | 51784341b755433e9f01472859a683b5 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:35:49.326 | INFO     | 51784341b755433e9f01472859a683b5 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:35:49.328 | INFO     | 51784341b755433e9f01472859a683b5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 25.795ms
2025-08-15 09:36:21.107 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-15 09:36:29.496 | INFO     | 7131e93e40104696805476248e92467e | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:29.498 | INFO     | 7131e93e40104696805476248e92467e | 成功认证Java用户: pythontest
2025-08-15 09:36:29.520 | INFO     | 7131e93e40104696805476248e92467e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:29.521 | INFO     | 7131e93e40104696805476248e92467e | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:36:29.613 | INFO     | 7131e93e40104696805476248e92467e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:36:29.615 | INFO     | 7131e93e40104696805476248e92467e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 125.687ms
2025-08-15 09:36:34.478 | INFO     | 4aac8daad08e432f915dd2540aedc369 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:34.479 | INFO     | 5fa592508f3349968e911583061a60aa | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:34.481 | INFO     | 4aac8daad08e432f915dd2540aedc369 | 成功认证Java用户: pythontest
2025-08-15 09:36:34.484 | INFO     | 5fa592508f3349968e911583061a60aa | 成功认证Java用户: pythontest
2025-08-15 09:36:34.513 | INFO     | 4aac8daad08e432f915dd2540aedc369 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:36:34.515 | INFO     | 4aac8daad08e432f915dd2540aedc369 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 38.199ms
2025-08-15 09:36:34.518 | INFO     | 49bd18e4782d4246abe2469c4ed061dd | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:34.520 | INFO     | 49bd18e4782d4246abe2469c4ed061dd | 成功认证Java用户: pythontest
2025-08-15 09:36:34.521 | INFO     | 5fa592508f3349968e911583061a60aa | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:34.522 | INFO     | 5fa592508f3349968e911583061a60aa | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:36:34.542 | INFO     | 5fa592508f3349968e911583061a60aa | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:36:34.545 | INFO     | 5fa592508f3349968e911583061a60aa | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 66.707ms
2025-08-15 09:36:34.551 | INFO     | 49bd18e4782d4246abe2469c4ed061dd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:34.552 | INFO     | 49bd18e4782d4246abe2469c4ed061dd | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:36:34.570 | INFO     | 49bd18e4782d4246abe2469c4ed061dd | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:36:34.572 | INFO     | 49bd18e4782d4246abe2469c4ed061dd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 53.536ms
2025-08-15 09:36:36.141 | INFO     | 328e534241ca4b15ade23e67fbfedfd2 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:36.143 | INFO     | 328e534241ca4b15ade23e67fbfedfd2 | 成功认证Java用户: pythontest
2025-08-15 09:36:36.149 | INFO     | 328e534241ca4b15ade23e67fbfedfd2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:36.150 | INFO     | 328e534241ca4b15ade23e67fbfedfd2 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:36:36.169 | INFO     | 328e534241ca4b15ade23e67fbfedfd2 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:36:36.171 | INFO     | 328e534241ca4b15ade23e67fbfedfd2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 31.263ms
2025-08-15 09:36:39.062 | INFO     | 37473d4915d4490f9dec5d04d3174943 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:39.063 | INFO     | 37473d4915d4490f9dec5d04d3174943 | 成功认证Java用户: pythontest
2025-08-15 09:36:39.069 | INFO     | 37473d4915d4490f9dec5d04d3174943 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:39.070 | INFO     | 37473d4915d4490f9dec5d04d3174943 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:36:39.083 | INFO     | 37473d4915d4490f9dec5d04d3174943 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/67c9904a346411f089c72e091ca444e4 "HTTP/1.1 200 OK"
2025-08-15 09:36:39.085 | INFO     | 37473d4915d4490f9dec5d04d3174943 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/67c9904a346411f089c72e091ca444e4 | 23.476ms
2025-08-15 09:36:41.411 | INFO     | 7ed0d5fb812e4b5098906b1d83aa0bb8 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:41.413 | INFO     | 7ed0d5fb812e4b5098906b1d83aa0bb8 | 成功认证Java用户: pythontest
2025-08-15 09:36:41.421 | INFO     | 7ed0d5fb812e4b5098906b1d83aa0bb8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:41.422 | INFO     | 7ed0d5fb812e4b5098906b1d83aa0bb8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:36:41.440 | INFO     | 7ed0d5fb812e4b5098906b1d83aa0bb8 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:36:41.443 | INFO     | 7ed0d5fb812e4b5098906b1d83aa0bb8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 31.237ms
2025-08-15 09:36:41.452 | INFO     | 4baea990eb9d4ea69edc5ccdc8d4be99 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:41.454 | INFO     | 4baea990eb9d4ea69edc5ccdc8d4be99 | 成功认证Java用户: pythontest
2025-08-15 09:36:41.461 | INFO     | 4baea990eb9d4ea69edc5ccdc8d4be99 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:41.462 | INFO     | 4baea990eb9d4ea69edc5ccdc8d4be99 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:36:41.481 | INFO     | 4baea990eb9d4ea69edc5ccdc8d4be99 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:36:41.483 | INFO     | 4baea990eb9d4ea69edc5ccdc8d4be99 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/list/page=1&page_size=20&orderby=create_time&desc=true | 31.081ms
2025-08-15 09:36:58.139 | INFO     | 98186a0c8d544c2bba9948da107e111c | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:58.140 | INFO     | 98186a0c8d544c2bba9948da107e111c | 成功认证Java用户: pythontest
2025-08-15 09:36:58.158 | INFO     | 98186a0c8d544c2bba9948da107e111c | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:36:58.160 | INFO     | 98186a0c8d544c2bba9948da107e111c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.649ms
2025-08-15 09:36:58.163 | INFO     | eafc0329a1d84425b7bdc8aae735d49a | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:58.164 | INFO     | eafc0329a1d84425b7bdc8aae735d49a | 成功认证Java用户: pythontest
2025-08-15 09:36:58.172 | INFO     | eafc0329a1d84425b7bdc8aae735d49a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:58.173 | INFO     | eafc0329a1d84425b7bdc8aae735d49a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:36:58.192 | INFO     | eafc0329a1d84425b7bdc8aae735d49a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:36:58.194 | INFO     | eafc0329a1d84425b7bdc8aae735d49a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 31.172ms
2025-08-15 09:36:58.196 | INFO     | 53fce1b86cfe4c62ab57062594d5e16d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:58.197 | INFO     | 53fce1b86cfe4c62ab57062594d5e16d | 成功认证Java用户: pythontest
2025-08-15 09:36:58.204 | INFO     | 53fce1b86cfe4c62ab57062594d5e16d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:58.205 | INFO     | 53fce1b86cfe4c62ab57062594d5e16d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:36:58.226 | INFO     | 53fce1b86cfe4c62ab57062594d5e16d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:36:58.228 | INFO     | 53fce1b86cfe4c62ab57062594d5e16d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 31.574ms
2025-08-15 09:36:59.184 | INFO     | 353378c515684426b1ca395b490f063b | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:36:59.185 | INFO     | 353378c515684426b1ca395b490f063b | 成功认证Java用户: pythontest
2025-08-15 09:36:59.191 | INFO     | 353378c515684426b1ca395b490f063b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:36:59.191 | INFO     | 353378c515684426b1ca395b490f063b | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:36:59.205 | INFO     | 353378c515684426b1ca395b490f063b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:36:59.207 | INFO     | 353378c515684426b1ca395b490f063b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 23.512ms
2025-08-15 09:37:00.553 | INFO     | 499d21933c7e4d4591a8a2e144377cc9 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:37:00.554 | INFO     | 499d21933c7e4d4591a8a2e144377cc9 | 成功认证Java用户: pythontest
2025-08-15 09:37:00.560 | INFO     | 499d21933c7e4d4591a8a2e144377cc9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:37:00.560 | INFO     | 499d21933c7e4d4591a8a2e144377cc9 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:37:00.573 | INFO     | 499d21933c7e4d4591a8a2e144377cc9 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:37:00.575 | INFO     | 499d21933c7e4d4591a8a2e144377cc9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 22.182ms
2025-08-15 09:37:01.360 | INFO     | 098a245e53dd482f95d48dd56ce5a835 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:37:01.362 | INFO     | 098a245e53dd482f95d48dd56ce5a835 | 成功认证Java用户: pythontest
2025-08-15 09:37:01.367 | INFO     | 098a245e53dd482f95d48dd56ce5a835 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:37:01.368 | INFO     | 098a245e53dd482f95d48dd56ce5a835 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:37:01.380 | INFO     | 098a245e53dd482f95d48dd56ce5a835 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:37:01.381 | INFO     | 098a245e53dd482f95d48dd56ce5a835 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 21.966ms
2025-08-15 09:37:06.597 | INFO     | 71f28539febf421e9d309eea85dd19cb | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:37:06.599 | INFO     | 71f28539febf421e9d309eea85dd19cb | 成功认证Java用户: pythontest
2025-08-15 09:37:06.605 | INFO     | 71f28539febf421e9d309eea85dd19cb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:37:06.606 | INFO     | 71f28539febf421e9d309eea85dd19cb | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:37:06.626 | INFO     | 71f28539febf421e9d309eea85dd19cb | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/67c9904a346411f089c72e091ca444e4 "HTTP/1.1 200 OK"
2025-08-15 09:37:06.629 | INFO     | 71f28539febf421e9d309eea85dd19cb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/67c9904a346411f089c72e091ca444e4 | 32.065ms
2025-08-15 09:37:21.676 | INFO     | 37bdffb5434849558e22a180a36019a2 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:37:21.677 | INFO     | 37bdffb5434849558e22a180a36019a2 | 成功认证Java用户: pythontest
2025-08-15 09:37:21.687 | INFO     | 37bdffb5434849558e22a180a36019a2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:37:21.688 | INFO     | 37bdffb5434849558e22a180a36019a2 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-15 09:37:21.738 | INFO     | 37bdffb5434849558e22a180a36019a2 | HTTP Request: POST http://192.168.66.40:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-15 09:37:21.740 | INFO     | 37bdffb5434849558e22a180a36019a2 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 64.310ms
2025-08-15 09:40:40.827 | INFO     | fa04e107377c4f8d839a52ab474e7ad0 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:40:40.829 | INFO     | 584152dc7ef4472bb496352c1d777925 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:40:40.829 | INFO     | ff8e7d3920da4325873883c7af4b0c5d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:40:40.831 | INFO     | fa04e107377c4f8d839a52ab474e7ad0 | 成功认证Java用户: pythontest
2025-08-15 09:40:40.833 | INFO     | 584152dc7ef4472bb496352c1d777925 | 成功认证Java用户: pythontest
2025-08-15 09:40:40.841 | INFO     | ff8e7d3920da4325873883c7af4b0c5d | 成功认证Java用户: pythontest
2025-08-15 09:40:40.850 | INFO     | ff8e7d3920da4325873883c7af4b0c5d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:40:40.852 | INFO     | ff8e7d3920da4325873883c7af4b0c5d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:40:40.856 | INFO     | 584152dc7ef4472bb496352c1d777925 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:40:40.857 | INFO     | 584152dc7ef4472bb496352c1d777925 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:40:40.861 | INFO     | fa04e107377c4f8d839a52ab474e7ad0 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:40:40.866 | INFO     | fa04e107377c4f8d839a52ab474e7ad0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 38.741ms
2025-08-15 09:40:40.885 | INFO     | ff8e7d3920da4325873883c7af4b0c5d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:40:40.887 | INFO     | 584152dc7ef4472bb496352c1d777925 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:40:40.888 | INFO     | ff8e7d3920da4325873883c7af4b0c5d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 59.882ms
2025-08-15 09:40:40.890 | INFO     | 584152dc7ef4472bb496352c1d777925 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 62.102ms
2025-08-15 09:40:46.305 | INFO     | e33b4c85a88a4e5392b80770515275e4 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:40:46.307 | INFO     | e33b4c85a88a4e5392b80770515275e4 | 成功认证Java用户: pythontest
2025-08-15 09:40:46.315 | INFO     | e33b4c85a88a4e5392b80770515275e4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:40:46.316 | INFO     | e33b4c85a88a4e5392b80770515275e4 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:40:46.338 | INFO     | e33b4c85a88a4e5392b80770515275e4 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:40:46.341 | INFO     | e33b4c85a88a4e5392b80770515275e4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 34.862ms
2025-08-15 09:40:49.228 | INFO     | 6526b1ab088b40a9acbfd09cfc02a597 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:40:49.230 | INFO     | 6526b1ab088b40a9acbfd09cfc02a597 | 成功认证Java用户: pythontest
2025-08-15 09:40:49.237 | INFO     | 6526b1ab088b40a9acbfd09cfc02a597 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:40:49.239 | INFO     | 6526b1ab088b40a9acbfd09cfc02a597 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:40:49.260 | INFO     | 6526b1ab088b40a9acbfd09cfc02a597 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:40:49.262 | INFO     | 6526b1ab088b40a9acbfd09cfc02a597 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 34.033ms
2025-08-15 09:40:49.272 | INFO     | e51dd95375fc4022bcb512420a4301a2 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:40:49.273 | INFO     | e51dd95375fc4022bcb512420a4301a2 | 成功认证Java用户: pythontest
2025-08-15 09:40:49.283 | INFO     | e51dd95375fc4022bcb512420a4301a2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:40:49.283 | INFO     | e51dd95375fc4022bcb512420a4301a2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:40:49.302 | INFO     | e51dd95375fc4022bcb512420a4301a2 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:40:49.303 | INFO     | e51dd95375fc4022bcb512420a4301a2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/list/page=1&page_size=20&orderby=create_time&desc=true | 31.441ms
2025-08-15 09:45:31.980 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-15 09:45:35.873 | INFO     | 83d8d91bb1404c77a7cc8c7c83d5d56a | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:45:35.874 | INFO     | 83d8d91bb1404c77a7cc8c7c83d5d56a | 成功认证Java用户: pythontest
2025-08-15 09:45:35.898 | INFO     | 83d8d91bb1404c77a7cc8c7c83d5d56a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:45:35.899 | INFO     | 83d8d91bb1404c77a7cc8c7c83d5d56a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:45:35.946 | INFO     | 83d8d91bb1404c77a7cc8c7c83d5d56a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:45:35.947 | INFO     | 83d8d91bb1404c77a7cc8c7c83d5d56a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=100 | 77.732ms
2025-08-15 09:45:35.957 | INFO     | dd6ae693ad9c4c76b56725798423a4bb | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:45:35.959 | INFO     | dd6ae693ad9c4c76b56725798423a4bb | 成功认证Java用户: pythontest
2025-08-15 09:45:35.979 | INFO     | dd6ae693ad9c4c76b56725798423a4bb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:45:35.980 | INFO     | dd6ae693ad9c4c76b56725798423a4bb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:45:36.000 | INFO     | dd6ae693ad9c4c76b56725798423a4bb | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16/documents?page=1&page_size=20&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:45:36.001 | INFO     | dd6ae693ad9c4c76b56725798423a4bb | 127.0.0.1       | GET      | 200    | /api/iot/v1/documents/87a3346677ee11f0a8f37e63526e5b16/list/page=1&page_size=20&orderby=create_time&desc=true | 44.553ms
2025-08-15 09:45:37.535 | INFO     | 49968e79665d46cc8ceee2552907a485 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:45:37.536 | INFO     | 28916cb265a84e5a9a232ea541ff296b | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:45:37.538 | INFO     | 49968e79665d46cc8ceee2552907a485 | 成功认证Java用户: pythontest
2025-08-15 09:45:37.542 | INFO     | 28916cb265a84e5a9a232ea541ff296b | 成功认证Java用户: pythontest
2025-08-15 09:45:37.557 | INFO     | 28916cb265a84e5a9a232ea541ff296b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:45:37.559 | INFO     | 28916cb265a84e5a9a232ea541ff296b | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:45:37.565 | INFO     | 49968e79665d46cc8ceee2552907a485 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:45:37.567 | INFO     | 49968e79665d46cc8ceee2552907a485 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 31.950ms
2025-08-15 09:45:37.571 | INFO     | 69879ae29fa34fe3bc70e53e4f485c5e | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:45:37.573 | INFO     | 69879ae29fa34fe3bc70e53e4f485c5e | 成功认证Java用户: pythontest
2025-08-15 09:45:37.579 | INFO     | 69879ae29fa34fe3bc70e53e4f485c5e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:45:37.580 | INFO     | 69879ae29fa34fe3bc70e53e4f485c5e | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:45:37.585 | INFO     | 28916cb265a84e5a9a232ea541ff296b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:45:37.588 | INFO     | 28916cb265a84e5a9a232ea541ff296b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.007ms
2025-08-15 09:45:37.601 | INFO     | 69879ae29fa34fe3bc70e53e4f485c5e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:45:37.603 | INFO     | 69879ae29fa34fe3bc70e53e4f485c5e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 32.301ms
2025-08-15 09:45:38.670 | INFO     | 073a8498190842e8aa7dba240a636f97 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:45:38.672 | INFO     | 073a8498190842e8aa7dba240a636f97 | 成功认证Java用户: pythontest
2025-08-15 09:45:38.678 | INFO     | 073a8498190842e8aa7dba240a636f97 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:45:38.680 | INFO     | 073a8498190842e8aa7dba240a636f97 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:45:38.701 | INFO     | 073a8498190842e8aa7dba240a636f97 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:45:38.703 | INFO     | 073a8498190842e8aa7dba240a636f97 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 33.055ms
2025-08-15 09:45:39.718 | INFO     | c6b51a7381014c989d34585c0bd6638f | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:45:39.720 | INFO     | c6b51a7381014c989d34585c0bd6638f | 成功认证Java用户: pythontest
2025-08-15 09:45:39.728 | INFO     | c6b51a7381014c989d34585c0bd6638f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:45:39.728 | INFO     | c6b51a7381014c989d34585c0bd6638f | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 09:45:39.742 | INFO     | c6b51a7381014c989d34585c0bd6638f | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 09:45:39.744 | INFO     | c6b51a7381014c989d34585c0bd6638f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 25.233ms
2025-08-15 09:45:45.024 | INFO     | 1ca75d986dd847d2a13bd32c5c90a6e7 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:45:45.027 | INFO     | 1ca75d986dd847d2a13bd32c5c90a6e7 | 成功认证Java用户: pythontest
2025-08-15 09:45:45.050 | INFO     | 1ca75d986dd847d2a13bd32c5c90a6e7 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:45:45.052 | INFO     | 1ca75d986dd847d2a13bd32c5c90a6e7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 27.680ms
2025-08-15 09:45:59.143 | INFO     | 59005e8db438405db6a3f24a66df208f | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:45:59.145 | INFO     | 59005e8db438405db6a3f24a66df208f | 成功认证Java用户: pythontest
2025-08-15 09:45:59.162 | INFO     | 59005e8db438405db6a3f24a66df208f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:45:59.163 | INFO     | 59005e8db438405db6a3f24a66df208f | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-15 09:45:59.180 | INFO     | 59005e8db438405db6a3f24a66df208f | HTTP Request: POST http://192.168.66.40:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-15 09:45:59.183 | INFO     | 59005e8db438405db6a3f24a66df208f | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 40.344ms
2025-08-15 09:46:20.442 | INFO     | 733035b82fa34079a181b90b588f5bbc | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:46:20.444 | INFO     | 733035b82fa34079a181b90b588f5bbc | 成功认证Java用户: pythontest
2025-08-15 09:46:20.463 | INFO     | 733035b82fa34079a181b90b588f5bbc | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:46:20.465 | INFO     | 733035b82fa34079a181b90b588f5bbc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.027ms
2025-08-15 09:46:20.467 | INFO     | 580390314f8347de944bed0a794b76ff | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:46:20.469 | INFO     | 580390314f8347de944bed0a794b76ff | 成功认证Java用户: pythontest
2025-08-15 09:46:20.476 | INFO     | 580390314f8347de944bed0a794b76ff | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:46:20.477 | INFO     | 580390314f8347de944bed0a794b76ff | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:46:20.495 | INFO     | 580390314f8347de944bed0a794b76ff | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:46:20.496 | INFO     | 580390314f8347de944bed0a794b76ff | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 29.703ms
2025-08-15 09:46:20.498 | INFO     | b26d8a9c29c1423bb0f63dbcc859e82b | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:46:20.500 | INFO     | b26d8a9c29c1423bb0f63dbcc859e82b | 成功认证Java用户: pythontest
2025-08-15 09:46:20.507 | INFO     | b26d8a9c29c1423bb0f63dbcc859e82b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:46:20.508 | INFO     | b26d8a9c29c1423bb0f63dbcc859e82b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:46:20.526 | INFO     | b26d8a9c29c1423bb0f63dbcc859e82b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:46:20.528 | INFO     | b26d8a9c29c1423bb0f63dbcc859e82b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 28.642ms
2025-08-15 09:46:30.454 | INFO     | 5fc83dd4940745bd96680bf699973b4d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:46:30.455 | INFO     | c239403948d3432bb358fdcf852fe598 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:46:30.456 | INFO     | 5fc83dd4940745bd96680bf699973b4d | 成功认证Java用户: pythontest
2025-08-15 09:46:30.458 | INFO     | c239403948d3432bb358fdcf852fe598 | 成功认证Java用户: pythontest
2025-08-15 09:46:30.470 | INFO     | c239403948d3432bb358fdcf852fe598 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:46:30.470 | INFO     | c239403948d3432bb358fdcf852fe598 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:46:30.476 | INFO     | 5fc83dd4940745bd96680bf699973b4d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:46:30.478 | INFO     | 5fc83dd4940745bd96680bf699973b4d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 24.212ms
2025-08-15 09:46:30.480 | INFO     | 0dcce295aafa4596b21773d733a5ee15 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:46:30.483 | INFO     | 0dcce295aafa4596b21773d733a5ee15 | 成功认证Java用户: pythontest
2025-08-15 09:46:30.490 | INFO     | c239403948d3432bb358fdcf852fe598 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:46:30.492 | INFO     | 0dcce295aafa4596b21773d733a5ee15 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:46:30.493 | INFO     | 0dcce295aafa4596b21773d733a5ee15 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:46:30.499 | INFO     | c239403948d3432bb358fdcf852fe598 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 43.689ms
2025-08-15 09:46:30.515 | INFO     | 0dcce295aafa4596b21773d733a5ee15 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:46:30.517 | INFO     | 0dcce295aafa4596b21773d733a5ee15 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 36.510ms
2025-08-15 09:47:17.461 | INFO     | d1558336adad48c48c3b174d9e608454 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:47:17.464 | INFO     | d1558336adad48c48c3b174d9e608454 | 成功认证Java用户: pythontest
2025-08-15 09:47:17.481 | INFO     | d1558336adad48c48c3b174d9e608454 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 09:47:17.483 | INFO     | d1558336adad48c48c3b174d9e608454 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.470ms
2025-08-15 09:47:17.485 | INFO     | 913ed872ea2c47cc887a76b158ff4319 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:47:17.486 | INFO     | 913ed872ea2c47cc887a76b158ff4319 | 成功认证Java用户: pythontest
2025-08-15 09:47:17.496 | INFO     | 913ed872ea2c47cc887a76b158ff4319 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:47:17.498 | INFO     | 913ed872ea2c47cc887a76b158ff4319 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 09:47:17.516 | INFO     | 913ed872ea2c47cc887a76b158ff4319 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:47:17.518 | INFO     | 913ed872ea2c47cc887a76b158ff4319 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 33.651ms
2025-08-15 09:47:17.520 | INFO     | 2c329caf73614c8495a4fa193358e143 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:47:17.522 | INFO     | 2c329caf73614c8495a4fa193358e143 | 成功认证Java用户: pythontest
2025-08-15 09:47:17.527 | INFO     | 2c329caf73614c8495a4fa193358e143 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:47:17.528 | INFO     | 2c329caf73614c8495a4fa193358e143 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 09:47:17.552 | INFO     | 2c329caf73614c8495a4fa193358e143 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 09:47:17.554 | INFO     | 2c329caf73614c8495a4fa193358e143 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 33.874ms
2025-08-15 09:49:16.331 | INFO     | 61784d34f2504441950f6ba7a60e0dea | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 09:49:16.335 | INFO     | 61784d34f2504441950f6ba7a60e0dea | 成功认证Java用户: pythontest
2025-08-15 09:49:16.342 | INFO     | 61784d34f2504441950f6ba7a60e0dea | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 09:49:16.342 | INFO     | 61784d34f2504441950f6ba7a60e0dea | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-15 09:49:16.361 | INFO     | 61784d34f2504441950f6ba7a60e0dea | HTTP Request: POST http://192.168.66.40:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-15 09:49:16.364 | INFO     | 61784d34f2504441950f6ba7a60e0dea | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 32.230ms
2025-08-15 10:00:09.368 | INFO     | 8e232ea26c1f4b98aa128c392be98f4f | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:00:09.369 | INFO     | 200482c18ffe406daf66bdeacef37f34 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:00:09.370 | INFO     | 61d3281ef6484628bec53051343265b9 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:00:09.431 | INFO     | 8e232ea26c1f4b98aa128c392be98f4f | 成功认证Java用户: pythontest
2025-08-15 10:00:09.442 | INFO     | 200482c18ffe406daf66bdeacef37f34 | 成功认证Java用户: pythontest
2025-08-15 10:00:09.444 | INFO     | 61d3281ef6484628bec53051343265b9 | 成功认证Java用户: pythontest
2025-08-15 10:00:09.836 | INFO     | 61d3281ef6484628bec53051343265b9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:00:09.837 | INFO     | 61d3281ef6484628bec53051343265b9 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:00:09.841 | INFO     | 200482c18ffe406daf66bdeacef37f34 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:00:09.841 | INFO     | 200482c18ffe406daf66bdeacef37f34 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:00:10.056 | INFO     | 200482c18ffe406daf66bdeacef37f34 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:00:10.057 | INFO     | 61d3281ef6484628bec53051343265b9 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:00:10.059 | INFO     | 8e232ea26c1f4b98aa128c392be98f4f | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:00:10.061 | INFO     | 200482c18ffe406daf66bdeacef37f34 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 691.335ms
2025-08-15 10:00:10.062 | INFO     | 61d3281ef6484628bec53051343265b9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 691.769ms
2025-08-15 10:00:10.063 | INFO     | 8e232ea26c1f4b98aa128c392be98f4f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 694.524ms
2025-08-15 10:01:40.402 | INFO     | 32a3242c4f5a44c6a7ebb55072c59028 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:01:40.403 | INFO     | fef39ed251be4230b2ac6d423d2edbeb | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:01:40.403 | INFO     | 98c91261395447e9b996bb826972cd28 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:01:40.404 | INFO     | 32a3242c4f5a44c6a7ebb55072c59028 | 成功认证Java用户: pythontest
2025-08-15 10:01:40.405 | INFO     | fef39ed251be4230b2ac6d423d2edbeb | 成功认证Java用户: pythontest
2025-08-15 10:01:40.405 | INFO     | 98c91261395447e9b996bb826972cd28 | 成功认证Java用户: pythontest
2025-08-15 10:01:40.414 | INFO     | 98c91261395447e9b996bb826972cd28 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:01:40.414 | INFO     | 98c91261395447e9b996bb826972cd28 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:01:40.418 | INFO     | fef39ed251be4230b2ac6d423d2edbeb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:01:40.418 | INFO     | fef39ed251be4230b2ac6d423d2edbeb | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:01:40.446 | INFO     | fef39ed251be4230b2ac6d423d2edbeb | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:01:40.449 | INFO     | 32a3242c4f5a44c6a7ebb55072c59028 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:01:40.451 | INFO     | fef39ed251be4230b2ac6d423d2edbeb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 48.095ms
2025-08-15 10:01:40.452 | INFO     | 98c91261395447e9b996bb826972cd28 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:01:40.453 | INFO     | 32a3242c4f5a44c6a7ebb55072c59028 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 50.869ms
2025-08-15 10:01:40.454 | INFO     | 98c91261395447e9b996bb826972cd28 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 50.881ms
2025-08-15 10:01:47.706 | INFO     | 9876f08f306941d6a993511a42138db1 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:01:47.708 | INFO     | 9876f08f306941d6a993511a42138db1 | 成功认证Java用户: pythontest
2025-08-15 10:01:47.724 | INFO     | 9876f08f306941d6a993511a42138db1 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:01:47.726 | INFO     | 9876f08f306941d6a993511a42138db1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 19.562ms
2025-08-15 10:01:47.730 | INFO     | bffe6990dad447589da9b3387b658d76 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:01:47.732 | INFO     | bffe6990dad447589da9b3387b658d76 | 成功认证Java用户: pythontest
2025-08-15 10:01:47.741 | INFO     | bffe6990dad447589da9b3387b658d76 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:01:47.742 | INFO     | bffe6990dad447589da9b3387b658d76 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:01:47.766 | INFO     | bffe6990dad447589da9b3387b658d76 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:01:47.767 | INFO     | bffe6990dad447589da9b3387b658d76 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 37.794ms
2025-08-15 10:01:47.771 | INFO     | b2aa5fa898de4f988f853f5114cc8cb3 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:01:47.774 | INFO     | b2aa5fa898de4f988f853f5114cc8cb3 | 成功认证Java用户: pythontest
2025-08-15 10:01:47.787 | INFO     | b2aa5fa898de4f988f853f5114cc8cb3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:01:47.788 | INFO     | b2aa5fa898de4f988f853f5114cc8cb3 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:01:47.812 | INFO     | b2aa5fa898de4f988f853f5114cc8cb3 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:01:47.813 | INFO     | b2aa5fa898de4f988f853f5114cc8cb3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 42.423ms
2025-08-15 10:01:49.649 | INFO     | 01e16640d07d42fd84ff379ddfad9061 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:01:49.651 | INFO     | 01e16640d07d42fd84ff379ddfad9061 | 成功认证Java用户: pythontest
2025-08-15 10:01:49.657 | INFO     | 01e16640d07d42fd84ff379ddfad9061 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:01:49.657 | INFO     | 01e16640d07d42fd84ff379ddfad9061 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 10:01:49.674 | INFO     | 01e16640d07d42fd84ff379ddfad9061 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/20853314797a11f08d9f7e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 10:01:49.675 | INFO     | 01e16640d07d42fd84ff379ddfad9061 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/20853314797a11f08d9f7e63526e5b16 | 25.568ms
2025-08-15 10:01:50.647 | INFO     | c703b76f1c2d4fe691ee2f1252e5d430 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:01:50.651 | INFO     | c703b76f1c2d4fe691ee2f1252e5d430 | 成功认证Java用户: pythontest
2025-08-15 10:01:50.657 | INFO     | c703b76f1c2d4fe691ee2f1252e5d430 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:01:50.657 | INFO     | c703b76f1c2d4fe691ee2f1252e5d430 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-15 10:01:50.673 | INFO     | c703b76f1c2d4fe691ee2f1252e5d430 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets/20853314797a11f08d9f7e63526e5b16 "HTTP/1.1 200 OK"
2025-08-15 10:01:50.675 | INFO     | c703b76f1c2d4fe691ee2f1252e5d430 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/20853314797a11f08d9f7e63526e5b16 | 28.272ms
2025-08-15 10:02:10.041 | INFO     | 9362f85e85be4972a0054b13730c0124 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:02:10.042 | INFO     | 9362f85e85be4972a0054b13730c0124 | 成功认证Java用户: pythontest
2025-08-15 10:02:10.048 | INFO     | 9362f85e85be4972a0054b13730c0124 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:02:10.048 | INFO     | 9362f85e85be4972a0054b13730c0124 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-15 10:02:10.731 | INFO     | 9362f85e85be4972a0054b13730c0124 | HTTP Request: POST http://192.168.66.40:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-15 10:02:10.732 | INFO     | 9362f85e85be4972a0054b13730c0124 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 692.067ms
2025-08-15 10:02:10.745 | INFO     | f63ec4df215c45b68a37e0435a4bb57e | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:02:10.746 | INFO     | 99b139aac3b94754a4e48ad664c0d66a | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:02:10.748 | INFO     | f63ec4df215c45b68a37e0435a4bb57e | 成功认证Java用户: pythontest
2025-08-15 10:02:10.749 | INFO     | 99b139aac3b94754a4e48ad664c0d66a | 成功认证Java用户: pythontest
2025-08-15 10:02:10.755 | INFO     | f63ec4df215c45b68a37e0435a4bb57e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:02:10.758 | INFO     | f63ec4df215c45b68a37e0435a4bb57e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:02:10.765 | INFO     | 99b139aac3b94754a4e48ad664c0d66a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:02:10.766 | INFO     | 99b139aac3b94754a4e48ad664c0d66a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:02:10.792 | INFO     | f63ec4df215c45b68a37e0435a4bb57e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:02:10.793 | INFO     | 99b139aac3b94754a4e48ad664c0d66a | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:02:10.795 | INFO     | 99b139aac3b94754a4e48ad664c0d66a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 49.955ms
2025-08-15 10:02:10.797 | INFO     | f63ec4df215c45b68a37e0435a4bb57e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.894ms
2025-08-15 10:04:57.698 | INFO     | 06588a28247c4ca48442a28434f979e4 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:04:57.699 | INFO     | 13f2605ebbbd4e01bd869cc536a03854 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:04:57.700 | INFO     | c3b3c13f27ba406e849b0b79ba3c2633 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:04:57.752 | INFO     | 06588a28247c4ca48442a28434f979e4 | 成功认证Java用户: pythontest
2025-08-15 10:04:57.753 | INFO     | 13f2605ebbbd4e01bd869cc536a03854 | 成功认证Java用户: pythontest
2025-08-15 10:04:57.754 | INFO     | c3b3c13f27ba406e849b0b79ba3c2633 | 成功认证Java用户: pythontest
2025-08-15 10:04:57.763 | INFO     | c3b3c13f27ba406e849b0b79ba3c2633 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:04:57.764 | INFO     | c3b3c13f27ba406e849b0b79ba3c2633 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:04:57.766 | INFO     | 13f2605ebbbd4e01bd869cc536a03854 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:04:57.767 | INFO     | 13f2605ebbbd4e01bd869cc536a03854 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:04:58.096 | INFO     | 13f2605ebbbd4e01bd869cc536a03854 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:04:58.096 | INFO     | 06588a28247c4ca48442a28434f979e4 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:04:58.099 | INFO     | c3b3c13f27ba406e849b0b79ba3c2633 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:04:58.100 | INFO     | 13f2605ebbbd4e01bd869cc536a03854 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 401.370ms
2025-08-15 10:04:58.101 | INFO     | 06588a28247c4ca48442a28434f979e4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 402.988ms
2025-08-15 10:04:58.102 | INFO     | 032b21d07eaa4c66a711d2b102d0bf8c | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:04:58.103 | INFO     | c3b3c13f27ba406e849b0b79ba3c2633 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 403.712ms
2025-08-15 10:04:58.104 | INFO     | 032b21d07eaa4c66a711d2b102d0bf8c | 成功认证Java用户: pythontest
2025-08-15 10:04:58.104 | INFO     | 1f286253071a49c291e824781db7b39d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:04:58.105 | INFO     | c187d145d3954bbf9283cf765fc35744 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:04:58.106 | INFO     | 1f286253071a49c291e824781db7b39d | 成功认证Java用户: pythontest
2025-08-15 10:04:58.107 | INFO     | c187d145d3954bbf9283cf765fc35744 | 成功认证Java用户: pythontest
2025-08-15 10:04:58.114 | INFO     | 032b21d07eaa4c66a711d2b102d0bf8c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:04:58.114 | INFO     | 032b21d07eaa4c66a711d2b102d0bf8c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:04:58.117 | INFO     | c187d145d3954bbf9283cf765fc35744 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:04:58.117 | INFO     | c187d145d3954bbf9283cf765fc35744 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:04:58.125 | INFO     | 1f286253071a49c291e824781db7b39d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:04:58.125 | INFO     | 1f286253071a49c291e824781db7b39d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.194ms
2025-08-15 10:04:58.137 | INFO     | 032b21d07eaa4c66a711d2b102d0bf8c | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:04:58.138 | INFO     | 032b21d07eaa4c66a711d2b102d0bf8c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 35.501ms
2025-08-15 10:04:58.140 | INFO     | c187d145d3954bbf9283cf765fc35744 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:04:58.141 | INFO     | c187d145d3954bbf9283cf765fc35744 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 35.526ms
2025-08-15 10:05:28.843 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-15 10:05:32.617 | INFO     | 383fdbb8c403400bab9b7ce55d1645e5 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:05:32.619 | INFO     | dcc14c3f880c4d13b13ba1ab8e76eda4 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:05:32.620 | INFO     | f2c9b07a3d484e9ea275829aac52d361 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:05:32.620 | INFO     | 383fdbb8c403400bab9b7ce55d1645e5 | 成功认证Java用户: pythontest
2025-08-15 10:05:32.622 | INFO     | dcc14c3f880c4d13b13ba1ab8e76eda4 | 成功认证Java用户: pythontest
2025-08-15 10:05:32.623 | INFO     | f2c9b07a3d484e9ea275829aac52d361 | 成功认证Java用户: pythontest
2025-08-15 10:05:32.655 | INFO     | dcc14c3f880c4d13b13ba1ab8e76eda4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:05:32.656 | INFO     | dcc14c3f880c4d13b13ba1ab8e76eda4 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:05:32.659 | INFO     | f2c9b07a3d484e9ea275829aac52d361 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:05:32.660 | INFO     | f2c9b07a3d484e9ea275829aac52d361 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:05:32.662 | INFO     | 383fdbb8c403400bab9b7ce55d1645e5 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:05:32.665 | INFO     | 383fdbb8c403400bab9b7ce55d1645e5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 51.828ms
2025-08-15 10:05:32.684 | INFO     | dcc14c3f880c4d13b13ba1ab8e76eda4 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:05:32.685 | INFO     | dcc14c3f880c4d13b13ba1ab8e76eda4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 67.891ms
2025-08-15 10:05:32.687 | INFO     | f2c9b07a3d484e9ea275829aac52d361 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:05:32.688 | INFO     | f2c9b07a3d484e9ea275829aac52d361 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 70.575ms
2025-08-15 10:05:40.130 | INFO     | bd9c803c1b95419fa98e6d254b68b0bb | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:05:40.132 | INFO     | bd9c803c1b95419fa98e6d254b68b0bb | 成功认证Java用户: pythontest
2025-08-15 10:05:40.150 | INFO     | bd9c803c1b95419fa98e6d254b68b0bb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:05:40.151 | INFO     | bd9c803c1b95419fa98e6d254b68b0bb | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-15 10:05:40.173 | INFO     | bd9c803c1b95419fa98e6d254b68b0bb | HTTP Request: POST http://192.168.66.40:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-15 10:05:40.175 | INFO     | bd9c803c1b95419fa98e6d254b68b0bb | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 45.324ms
2025-08-15 10:07:57.602 | INFO     | 5a5fd62c47fa49368cfa5fcd83830f7d | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:07:57.603 | INFO     | 544f6d40e16443a7a88b00c318f1f2cf | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:07:57.604 | INFO     | b090c8b17f4f4e7398b919c3e6ce4334 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:07:57.605 | INFO     | 5a5fd62c47fa49368cfa5fcd83830f7d | 成功认证Java用户: pythontest
2025-08-15 10:07:57.607 | INFO     | 544f6d40e16443a7a88b00c318f1f2cf | 成功认证Java用户: pythontest
2025-08-15 10:07:57.608 | INFO     | b090c8b17f4f4e7398b919c3e6ce4334 | 成功认证Java用户: pythontest
2025-08-15 10:07:57.621 | INFO     | b090c8b17f4f4e7398b919c3e6ce4334 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:07:57.622 | INFO     | b090c8b17f4f4e7398b919c3e6ce4334 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:07:57.627 | INFO     | 544f6d40e16443a7a88b00c318f1f2cf | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:07:57.628 | INFO     | 544f6d40e16443a7a88b00c318f1f2cf | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:07:57.634 | INFO     | 5a5fd62c47fa49368cfa5fcd83830f7d | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:07:57.638 | INFO     | 5a5fd62c47fa49368cfa5fcd83830f7d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 36.525ms
2025-08-15 10:07:57.659 | INFO     | b090c8b17f4f4e7398b919c3e6ce4334 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:07:57.661 | INFO     | 544f6d40e16443a7a88b00c318f1f2cf | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:07:57.664 | INFO     | b090c8b17f4f4e7398b919c3e6ce4334 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 60.730ms
2025-08-15 10:07:57.666 | INFO     | 544f6d40e16443a7a88b00c318f1f2cf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 63.342ms
2025-08-15 10:08:12.124 | INFO     | 97849f6f8d38405d9106ec25cc887d95 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:08:12.125 | INFO     | 97849f6f8d38405d9106ec25cc887d95 | 成功认证Java用户: pythontest
2025-08-15 10:08:12.145 | INFO     | 97849f6f8d38405d9106ec25cc887d95 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:08:12.146 | INFO     | 97849f6f8d38405d9106ec25cc887d95 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.876ms
2025-08-15 10:08:12.149 | INFO     | 905a9bd5a7a74a378d1cb4b9983d3290 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:08:12.150 | INFO     | 905a9bd5a7a74a378d1cb4b9983d3290 | 成功认证Java用户: pythontest
2025-08-15 10:08:12.156 | INFO     | 905a9bd5a7a74a378d1cb4b9983d3290 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:08:12.157 | INFO     | 905a9bd5a7a74a378d1cb4b9983d3290 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:08:12.174 | INFO     | 905a9bd5a7a74a378d1cb4b9983d3290 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:08:12.176 | INFO     | 905a9bd5a7a74a378d1cb4b9983d3290 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.062ms
2025-08-15 10:08:12.179 | INFO     | 6d7750fbef5c4ff386b7bbfda9eca14b | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:08:12.180 | INFO     | 6d7750fbef5c4ff386b7bbfda9eca14b | 成功认证Java用户: pythontest
2025-08-15 10:08:12.185 | INFO     | 6d7750fbef5c4ff386b7bbfda9eca14b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:08:12.186 | INFO     | 6d7750fbef5c4ff386b7bbfda9eca14b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:08:12.203 | INFO     | 6d7750fbef5c4ff386b7bbfda9eca14b | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:08:12.204 | INFO     | 6d7750fbef5c4ff386b7bbfda9eca14b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 25.354ms
2025-08-15 10:09:19.824 | INFO     | e50314ed892547ab9105d2e46aa20c06 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:09:19.825 | INFO     | 921dbc60c33f450aa60eb3bf902205c8 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:09:19.826 | INFO     | 15bf47f3638a41e484a5b20527749e7e | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:09:19.831 | INFO     | 921dbc60c33f450aa60eb3bf902205c8 | 成功认证Java用户: pythontest
2025-08-15 10:09:19.831 | INFO     | 15bf47f3638a41e484a5b20527749e7e | 成功认证Java用户: pythontest
2025-08-15 10:09:19.832 | INFO     | e50314ed892547ab9105d2e46aa20c06 | 成功认证Java用户: pythontest
2025-08-15 10:09:19.857 | INFO     | 921dbc60c33f450aa60eb3bf902205c8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:09:19.858 | INFO     | 921dbc60c33f450aa60eb3bf902205c8 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:09:19.861 | INFO     | 15bf47f3638a41e484a5b20527749e7e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:09:19.861 | INFO     | 15bf47f3638a41e484a5b20527749e7e | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:09:19.864 | INFO     | e50314ed892547ab9105d2e46aa20c06 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:09:19.866 | INFO     | e50314ed892547ab9105d2e46aa20c06 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 42.063ms
2025-08-15 10:09:19.868 | INFO     | 6b54bf2233bd4a3ea67c8a35952922fe | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:09:19.869 | INFO     | 6b54bf2233bd4a3ea67c8a35952922fe | 成功认证Java用户: pythontest
2025-08-15 10:09:19.888 | INFO     | 921dbc60c33f450aa60eb3bf902205c8 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:09:19.889 | INFO     | 15bf47f3638a41e484a5b20527749e7e | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:09:19.891 | INFO     | 921dbc60c33f450aa60eb3bf902205c8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 66.103ms
2025-08-15 10:09:19.894 | INFO     | 6b54bf2233bd4a3ea67c8a35952922fe | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-15 10:09:19.895 | INFO     | 647586b294ac4dd894de9fc33165c526 | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:09:19.896 | INFO     | 15bf47f3638a41e484a5b20527749e7e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 70.976ms
2025-08-15 10:09:19.897 | INFO     | 647586b294ac4dd894de9fc33165c526 | 成功认证Java用户: pythontest
2025-08-15 10:09:19.898 | INFO     | 6b54bf2233bd4a3ea67c8a35952922fe | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 29.545ms
2025-08-15 10:09:19.899 | INFO     | b632c4e287634b9e8d40fb62bfd5650c | JWT标准验证成功，获取UUID: a41cb1f6-0280-41ac-acac-756a67b71714
2025-08-15 10:09:19.900 | INFO     | b632c4e287634b9e8d40fb62bfd5650c | 成功认证Java用户: pythontest
2025-08-15 10:09:19.904 | INFO     | 647586b294ac4dd894de9fc33165c526 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:09:19.905 | INFO     | 647586b294ac4dd894de9fc33165c526 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-15 10:09:19.909 | INFO     | b632c4e287634b9e8d40fb62bfd5650c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-15 10:09:19.910 | INFO     | b632c4e287634b9e8d40fb62bfd5650c | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-15 10:09:19.922 | INFO     | 647586b294ac4dd894de9fc33165c526 | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:09:19.923 | INFO     | 647586b294ac4dd894de9fc33165c526 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.532ms
2025-08-15 10:09:19.932 | INFO     | b632c4e287634b9e8d40fb62bfd5650c | HTTP Request: GET http://192.168.66.40:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-15 10:09:19.934 | INFO     | b632c4e287634b9e8d40fb62bfd5650c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 34.449ms
