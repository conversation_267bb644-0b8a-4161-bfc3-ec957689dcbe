<template>
  <div class="document-preview-test">
    <h2>文档预览功能测试</h2>
    
    <el-card class="test-card">
      <template #header>
        <span>测试用例</span>
      </template>
      
      <div class="test-cases">
        <el-button 
          v-for="testCase in testCases" 
          :key="testCase.id"
          @click="selectTestCase(testCase)"
          :type="selectedCase?.id === testCase.id ? 'primary' : 'default'"
          class="test-case-btn"
        >
          {{ testCase.name }}
        </el-button>
      </div>
    </el-card>

    <el-card v-if="selectedCase" class="preview-card">
      <template #header>
        <span>预览测试: {{ selectedCase.name }}</span>
      </template>
      
      <div class="preview-container">
        <DocumentPreview 
          :document="selectedCase.document"
          :visible="true"
          :show-settings="false"
        />
      </div>
    </el-card>

    <el-card class="info-card">
      <template #header>
        <span>测试说明</span>
      </template>
      
      <div class="test-info">
        <h4>重构内容：</h4>
        <ul>
          <li>✅ 优化了 VueOfficePreview 组件，正确导入 CSS 样式</li>
          <li>✅ 清理了 Office 转 PDF 的相关逻辑</li>
          <li>✅ 更新了 Office 文档预览 URL 处理</li>
          <li>✅ 支持 Word (.docx, .doc) 和 Excel (.xlsx, .xls) 原生预览</li>
          <li>⚠️ PowerPoint (.pptx, .ppt) 暂不支持，需要下载查看</li>
        </ul>
        
        <h4>测试要点：</h4>
        <ul>
          <li>检查 Word 文档是否能正常预览</li>
          <li>检查 Excel 文档是否能正常预览</li>
          <li>检查 PowerPoint 文档是否显示不支持提示</li>
          <li>检查 PDF 文档预览是否正常（使用原有逻辑）</li>
          <li>检查错误处理是否正常</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DocumentPreview from './DocumentPreview.vue';
import type { DocumentInfo } from '/@/api/iot/document';

// 测试用例数据
const testCases = ref([
  {
    id: 'word-test',
    name: 'Word 文档测试',
    document: {
      id: 'test-word-doc',
      kb_id: 'test-kb',
      name: 'test-document.docx',
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      size: 1024000,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as DocumentInfo
  },
  {
    id: 'excel-test',
    name: 'Excel 文档测试',
    document: {
      id: 'test-excel-doc',
      kb_id: 'test-kb',
      name: 'test-spreadsheet.xlsx',
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      size: 512000,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as DocumentInfo
  },
  {
    id: 'ppt-test',
    name: 'PowerPoint 文档测试',
    document: {
      id: 'test-ppt-doc',
      kb_id: 'test-kb',
      name: 'test-presentation.pptx',
      type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      size: 2048000,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as DocumentInfo
  },
  {
    id: 'pdf-test',
    name: 'PDF 文档测试',
    document: {
      id: 'test-pdf-doc',
      kb_id: 'test-kb',
      name: 'test-document.pdf',
      type: 'application/pdf',
      size: 1536000,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as DocumentInfo
  }
]);

const selectedCase = ref<typeof testCases.value[0] | null>(null);

const selectTestCase = (testCase: typeof testCases.value[0]) => {
  selectedCase.value = testCase;
};
</script>

<style scoped>
.document-preview-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card,
.preview-card,
.info-card {
  margin-bottom: 20px;
}

.test-cases {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.test-case-btn {
  margin-bottom: 8px;
}

.preview-container {
  height: 600px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.test-info h4 {
  margin: 16px 0 8px 0;
  color: #303133;
}

.test-info ul {
  margin: 0 0 16px 0;
  padding-left: 20px;
}

.test-info li {
  margin-bottom: 4px;
  line-height: 1.5;
}
</style>
