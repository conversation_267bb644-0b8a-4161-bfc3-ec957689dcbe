2025-08-13 10:26:39.166 | ERROR    | f1eb1f7d029e44af9e2f3760ca6316a7 | 获取知识库列表失败: RAGFlow服务请求超时
2025-08-13 10:27:28.976 | ERROR    | d97da7bdf5d94b7496f7038cce8cb573 | 获取知识库列表失败: RAGFlow服务请求超时
2025-08-13 10:28:39.164 | ERROR    | 8c2b6b44c93d4ab4a0ae592750a42d47 | 获取知识库列表失败: RAGFlow服务连接失败: 
2025-08-13 10:29:14.061 | ERROR    | ec46393570c94e169df2a9e963e91b60 | 获取知识库列表失败: RAGFlow服务请求超时
2025-08-13 10:31:57.530 | ERROR    | 88c1ee022c4c495bbcb6e3606bfd16a0 | 获取知识库列表失败: RAGFlow服务请求超时
2025-08-13 10:37:32.077 | ERROR    | ed277572fffc431289330de7b9ae77f5 | 更新知识库失败: RAGFlow服务连接失败: 200: Unsupported model: <text-embedding-bge-m3@LM-Studio>
2025-08-13 10:49:35.883 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002D58ECBA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002D591228B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002D59122BA60>
    └ <uvicorn.server.Server object at 0x000002D59162F7D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002D59122BB00>
           │       │   └ <uvicorn.server.Server object at 0x000002D59162F7D0>
           │       └ <function run at 0x000002D590A6F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002D597F71EE0>
           │      └ <function Runner.run at 0x000002D590F0B2E0>
           └ <asyncio.runners.Runner object at 0x000002D597ECD040>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002D590F08EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002D597ECD040>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002D590FD8D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002D590F0AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002D590A64860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1812, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 55620)>
    └ <_ProactorSocketTransport closing fd=1812>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 11:00:18.803 | ERROR    | c437b6bfd54a4ccfa54afd01a80753d1 | Java token认证失败: 401: Token 格式错误
2025-08-13 11:07:01.618 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000024DF314A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000024DF5698B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000024DF569BA60>
    └ <uvicorn.server.Server object at 0x0000024DFC425F40>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000024DF569BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000024DFC425F40>
           │       └ <function run at 0x0000024DF4EDF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000024DFC42E7A0>
           │      └ <function Runner.run at 0x0000024DF537B2E0>
           └ <asyncio.runners.Runner object at 0x0000024DFC3DDC40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000024DF5378EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000024DFC3DDC40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000024DF5448D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000024DF537AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000024DF4ED4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1688, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 58579)>
    └ <_ProactorSocketTransport closing fd=1688>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 11:17:57.154 | ERROR    | a29ac6beafa844918b77afa328aff27f | Java token认证失败: 401: Token 格式错误
2025-08-13 11:19:05.989 | ERROR    | 432f24463d1a4345ae980d8d3a0148c5 | 获取嵌入模型列表失败: name 'logger' is not defined
2025-08-13 11:19:47.008 | ERROR    | c9296128bbc44f94b4ddf45cff4a1368 | 获取嵌入模型列表失败: name 'logger' is not defined
2025-08-13 11:20:31.420 | ERROR    | 20e0c00a09c945efb1061f5c52b1423c | 获取嵌入模型列表失败: 503: RAGFlow服务连接失败: 200: <NotFound '404: Not Found'>
2025-08-13 11:22:29.076 | ERROR    | bed3e3b09910452e84a7101abd63e59f | 获取嵌入模型列表失败: 503: RAGFlow服务连接失败: 200: <NotFound '404: Not Found'>
2025-08-13 11:22:46.185 | ERROR    | 5026616e832c425facc35ca58297db54 | 获取嵌入模型列表失败: 503: RAGFlow服务连接失败: 200: <NotFound '404: Not Found'>
2025-08-13 11:33:08.377 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002B447BFA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002B44A118B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002B44A11BA60>
    └ <uvicorn.server.Server object at 0x000002B450D85970>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002B44A11BB00>
           │       │   └ <uvicorn.server.Server object at 0x000002B450D85970>
           │       └ <function run at 0x000002B44990F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002B450ECE960>
           │      └ <function Runner.run at 0x000002B449DAB2E0>
           └ <asyncio.runners.Runner object at 0x000002B4507009E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002B449DA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002B4507009E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002B449E78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002B449DAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002B449904860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1964, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 64360)>
    └ <_ProactorSocketTransport closing fd=1964>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 11:33:08.383 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002B447BFA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002B44A118B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002B44A11BA60>
    └ <uvicorn.server.Server object at 0x000002B450D85970>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002B44A11BB00>
           │       │   └ <uvicorn.server.Server object at 0x000002B450D85970>
           │       └ <function run at 0x000002B44990F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002B450ECE960>
           │      └ <function Runner.run at 0x000002B449DAB2E0>
           └ <asyncio.runners.Runner object at 0x000002B4507009E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002B449DA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002B4507009E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002B449E78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002B449DAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002B449904860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1968, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 64361)>
    └ <_ProactorSocketTransport closing fd=1968>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 11:33:08.465 | ERROR    | 7496179967a5480ebe4ec374e14b928f | 获取嵌入模型列表失败: 503: RAGFlow服务连接失败: 200: <NotFound '404: Not Found'>
2025-08-13 14:10:59.190 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002565D2BA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002565F7C8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002565F7CBA60>
    └ <uvicorn.server.Server object at 0x0000025666305850>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002565F7CBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000025666305850>
           │       └ <function run at 0x000002565EFCF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000256665903C0>
           │      └ <function Runner.run at 0x000002565F06B2E0>
           └ <asyncio.runners.Runner object at 0x00000256664F3E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002565F068EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000256664F3E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002565F138D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002565F06AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002565EFC4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1180, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 60320)>
    └ <_ProactorSocketTransport closing fd=1180>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 14:10:59.304 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002565D2BA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002565F7C8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002565F7CBA60>
    └ <uvicorn.server.Server object at 0x0000025666305850>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002565F7CBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000025666305850>
           │       └ <function run at 0x000002565EFCF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000256665903C0>
           │      └ <function Runner.run at 0x000002565F06B2E0>
           └ <asyncio.runners.Runner object at 0x00000256664F3E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002565F068EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000256664F3E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002565F138D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002565F06AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002565EFC4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1480, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 60321)>
    └ <_ProactorSocketTransport closing fd=1480>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 15:28:30.278 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x00000184512EA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x00000184537A8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000184537ABA60>
    └ <uvicorn.server.Server object at 0x0000018453123350>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000184537ABB00>
           │       │   └ <uvicorn.server.Server object at 0x0000018453123350>
           │       └ <function run at 0x0000018452FAF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001845A678740>
           │      └ <function Runner.run at 0x000001845304B2E0>
           └ <asyncio.runners.Runner object at 0x000001845955C890>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000018453048EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001845955C890>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000018453118D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001845304AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000018452FA4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1844, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 58754)>
    └ <_ProactorSocketTransport closing fd=1844>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-13 16:35:46.031 | ERROR    | ff85d7350da9497791e5bb95e8b71898 | 获取文档详情失败: RAGFlow文档服务连接失败: 200: You do not own the dataset cache.
2025-08-13 16:36:07.363 | ERROR    | cf097bb08ff2472db7ce8ef708392306 | 获取文档列表失败: RAGFlow文档服务连接失败: 200: You don't own the dataset 87a3346677ee111f0a8f37e63526e5b16. 
2025-08-13 16:36:52.633 | ERROR    | 172eb38a173041d7b405968890e67947 | 获取文档预览失败: RAGFlow文档服务连接失败: 200: The dataset not own the document 5b49ded077f611f0836577e63526e5b16.
2025-08-13 16:37:03.173 | ERROR    | 1e994639f81f4d46b092fff8f9bca338 | 获取文档详情失败: RAGFlow文档服务连接失败: 200: You do not own the dataset cache.
2025-08-13 16:37:24.206 | ERROR    | 7d09377bfc8f4959a6ac1309d166942a | 获取文档详情失败: RAGFlow文档服务连接失败: 200: You do not own the dataset conversion-progress.
2025-08-13 16:47:40.981 | ERROR    | fbd69661f79e49578878724770b41424 | 文档转换失败: cannot import name 'CustomException' from 'backend.common.exception.errors' (C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\exception\errors.py)
2025-08-13 16:49:02.556 | ERROR    | e117693b56b94d01ab52f53f11c8e857 | 文档转换失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:55:17.441 | ERROR    | 6d760c6e46df493ab148ae173ea7ca05 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:55:38.992 | ERROR    | 89391aaed9544caf89a6667d2f81b894 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:55:39.023 | ERROR    | bc93c53d3031423599bb20a1ff5d2201 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:55:39.712 | ERROR    | e3fa02c04a0546e09a785944f113476e | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:59:08.678 | ERROR    | 340db2eb570e49b2aebc422c7ed6b588 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:59:10.269 | ERROR    | bafc7df3f9914c2d8b398a6602d09616 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:59:10.762 | ERROR    | 8cd68390635048f8abd3f60fbde31fa1 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:59:11.180 | ERROR    | e9201d82c39c47528571e7d19a1b3c78 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:59:11.488 | ERROR    | 2180a69e5d00423d82bc2f4258fb0760 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:59:11.740 | ERROR    | ea15d625cadb48d2a930ad29550eb798 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 16:59:12.254 | ERROR    | ecfa8cda209843baa342ede1c9f381cd | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:00:19.206 | ERROR    | fbbf20c906bc44a8bcbf8f310d9e8278 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:00:20.937 | ERROR    | 3e1368ba8eac4b198fc9709e08cd5e0e | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:00:21.354 | ERROR    | 77416d68928144fc8fc7ed84ca2d81b4 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:10:19.491 | ERROR    | f39f86faf6ff4d76ad600239217f7bde | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:10:29.210 | ERROR    | 50cfd0a69ffa4606b195905e5298ef61 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:10:36.106 | ERROR    | efe0265ba9544f04b3cbd6900dfa80a9 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:11:12.009 | ERROR    | 2581b5981056437e919f64daddc9c180 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:11:21.690 | ERROR    | 74d67189dc914ef59e7fc8413f1c5026 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:11:35.219 | ERROR    | 220cd023973b4f7ab28ea9a2d80c595a | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:11:49.128 | ERROR    | 6d317091aa2c4e49be27005d6b744835 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:12:04.594 | ERROR    | 89910fa1d1d941bba08973df0ece83ce | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:12:21.677 | ERROR    | add698a749074f09acb3372451bd0132 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:12:30.094 | ERROR    | 21b344ab3b2748cc82ba293dce9311b4 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:13:06.476 | ERROR    | c2d956b089c54185b0779a5f9659c584 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:14:11.420 | ERROR    | f342712ed70743fe921298e17f4d8a39 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:15:05.701 | ERROR    | 8c479ad4e62c45ff93258807924d9456 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:24:23.955 | ERROR    | a60e4e16476e429484ad613e04bb42f7 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:24:31.522 | ERROR    | e0e50803f5fb4034bf3ecd2a6ec3a7cb | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:29:42.223 | ERROR    | f40676afa87b4c42841f268a0bf63919 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:30:09.538 | ERROR    | 382e46802ab34fe7b85520fe3b5f929f | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:31:30.575 | ERROR    | 1a7c2959c3e7472eaa646245c63b27bb | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:31:36.390 | ERROR    | a20c70bb866d486eb9fa3538815eeff1 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:31:41.137 | ERROR    | efa2a703e6054aad87166245b2022e24 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:31:43.801 | ERROR    | f3b7d067b0954e3992da7a81dfa4fb6c | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:31:47.723 | ERROR    | 7fbda958cf7b468f824029a9f17b8b23 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:31:50.128 | ERROR    | 69adb143d8b9464b9a59e918610b0d10 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:32:28.417 | ERROR    | 9406ebb6758241a290c2cfe71537cb90 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:32:30.553 | ERROR    | b13aae2d459a4e239c2689d0a98f56f7 | 获取文档预览失败: RAGFlow文档服务连接失败: Expecting value: line 1 column 1 (char 0)
2025-08-13 17:33:21.303 | ERROR    | 20e5a2b23581448ea72b9930498eb3a5 | Java token认证失败: 401: Token 格式错误
