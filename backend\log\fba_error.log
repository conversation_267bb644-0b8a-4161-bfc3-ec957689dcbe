2025-08-15 09:26:17.090 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002678BE7A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002678E368B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002678E36BA60>
    └ <uvicorn.server.Server object at 0x000002679517D160>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002678E36BB00>
           │       │   └ <uvicorn.server.Server object at 0x000002679517D160>
           │       └ <function run at 0x000002678C59F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026796233920>
           │      └ <function Runner.run at 0x000002678E04B2E0>
           └ <asyncio.runners.Runner object at 0x0000026794ED0650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002678E048EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026794ED0650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002678E118D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002678E04AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002678C594860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1480, family=2, type=1, proto=6, laddr=('*************', 63401), raddr=('*************', 5981)>
    └ <_ProactorSocketTransport closing fd=1480>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-15 09:26:17.295 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002678BE7A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000002678E368B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000002678E36BA60>
    └ <uvicorn.server.Server object at 0x000002679517D160>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002678E36BB00>
           │       │   └ <uvicorn.server.Server object at 0x000002679517D160>
           │       └ <function run at 0x000002678C59F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026796233920>
           │      └ <function Runner.run at 0x000002678E04B2E0>
           └ <asyncio.runners.Runner object at 0x0000026794ED0650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002678E048EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026794ED0650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002678E118D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002678E04AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002678C594860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1892, family=2, type=1, proto=6, laddr=('*************', 64070), raddr=('*************', 5981)>
    └ <_ProactorSocketTransport closing fd=1892>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-15 09:36:29.614 | ERROR    | 7131e93e40104696805476248e92467e | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 09:36:36.171 | ERROR    | ******************************** | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 09:36:39.084 | ERROR    | 37473d4915d4490f9dec5d04d3174943 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 09:36:59.206 | ERROR    | 353378c515684426b1ca395b490f063b | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 09:37:00.575 | ERROR    | 499d21933c7e4d4591a8a2e144377cc9 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 09:37:01.381 | ERROR    | 098a245e53dd482f95d48dd56ce5a835 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 09:37:06.628 | ERROR    | 71f28539febf421e9d309eea85dd19cb | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 09:37:21.739 | ERROR    | 37bdffb5434849558e22a180a36019a2 | 创建知识库失败: RAGFlow服务连接失败: 200: Field: <embedding_model> - Message: <Embedding model identifier must follow <model_name>@<provider> format> - Value: <bge-m3:latest>
2025-08-15 09:45:38.703 | ERROR    | 073a8498190842e8aa7dba240a636f97 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 09:45:39.743 | ERROR    | c6b51a7381014c989d34585c0bd6638f | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 09:45:59.183 | ERROR    | 59005e8db438405db6a3f24a66df208f | 创建知识库失败: RAGFlow服务连接失败: 200: Field: <embedding_model> - Message: <Embedding model identifier must follow <model_name>@<provider> format> - Value: <bge-m3:latest>
2025-08-15 09:49:16.363 | ERROR    | 61784d34f2504441950f6ba7a60e0dea | 创建知识库失败: RAGFlow服务连接失败: 200: Field: <embedding_model> - Message: <Embedding model identifier must follow <model_name>@<provider> format> - Value: <BAAI/bge-m3>
2025-08-15 10:01:49.674 | ERROR    | 01e16640d07d42fd84ff379ddfad9061 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
2025-08-15 10:01:50.675 | ERROR    | c703b76f1c2d4fe691ee2f1252e5d430 | 获取知识库详情失败: RAGFlow服务连接失败: 200: <MethodNotAllowed '405: Method Not Allowed'>
