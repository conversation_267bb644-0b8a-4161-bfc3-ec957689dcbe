#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库服务层 - 简化版本

提供知识库管理的纯代理服务，直接调用RAGFlow API
"""
from typing import List
import httpx
from fastapi import HTTPException
from loguru import logger

from backend.app.iot.schema.knowledge_base import (
    KnowledgeBaseCreate,
    KnowledgeBaseQuery,
    KnowledgeBaseUpdate
)
from backend.core.conf import settings


class KnowledgeBaseService:
    """知识库服务类 - 简化版本，纯代理模式"""

    def __init__(self):
        # RAGFlow服务配置
        self.ragflow_base_url = getattr(settings, 'KNOWLEDGE_BASE_URL', 'http://192.168.66.40:6610')
        self.ragflow_api_key = getattr(settings, 'KNOWLEDGE_BASE_API_KEY', 'ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW')
        self.timeout = getattr(settings, 'KNOWLEDGE_BASE_TIMEOUT', 30.0)

    async def _make_kb_request(self, method: str, endpoint: str, **kwargs) -> dict:
        """
        统一的RAGFlow请求处理 - 保持原始错误信息

        :param method: HTTP 方法
        :param endpoint: API 端点
        :param kwargs: 请求参数 (data, params, json等)
        :return: 响应数据
        """
        url = f"{self.ragflow_base_url}{endpoint}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.ragflow_api_key}"
        }

        async with httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout, connect=10.0),
            verify=False,
            headers={"User-Agent": "FastAPI-Best-Architecture/1.0"},
            follow_redirects=True
        ) as client:
            try:
                response = await client.request(
                    method=method.upper(),
                    url=url,
                    headers=headers,
                    **kwargs
                )

                result = response.json()

                # 保持RAGFlow原始错误信息
                if response.status_code != 200 or result.get("code") != 0:
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=result.get("message", "RAGFlow服务错误")
                    )

                return result

            except httpx.TimeoutException:
                raise HTTPException(status_code=504, detail="RAGFlow服务请求超时")
            except httpx.HTTPStatusError as e:
                raise HTTPException(status_code=e.response.status_code, detail=f"RAGFlow服务错误: {e.response.text}")
            except Exception as e:
                raise HTTPException(status_code=503, detail=f"RAGFlow服务连接失败: {str(e)}")

    async def health_check(self) -> dict:
        """
        检查知识库服务健康状态

        :return: 健康状态信息
        """
        try:
            # 尝试连接RAGFlow服务 - 获取数据集列表
            response = await self._make_kb_request("GET", "/api/v1/datasets", params={"page": 1, "page_size": 1})

            # 检查响应格式
            if isinstance(response, dict) and response.get("code") == 0:
                return {
                    "status": "healthy",
                    "service_url": self.ragflow_base_url,
                    "message": "RAGFlow服务连接正常"
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "service_url": self.ragflow_base_url,
                "error": str(e),
                "message": "RAGFlow服务连接失败"
            }

    # ❌ 删除 _convert_to_kb_info 方法 - 不必要的数据转换


    async def create_knowledge_base(self, kb_data: KnowledgeBaseCreate) -> dict:
        """
        创建知识库 - 直接代理RAGFlow

        :param kb_data: 知识库创建数据
        :return: RAGFlow响应数据
        """
        data = kb_data.model_dump(exclude_none=True)
        return await self._make_kb_request("POST", "/api/v1/datasets", json=data)
    
    async def get_knowledge_base(self, kb_id: str) -> dict:
        """
        获取知识库详情 - 直接代理RAGFlow

        :param kb_id: 知识库ID
        :return: RAGFlow响应数据
        """
        return await self._make_kb_request("GET", f"/api/v1/datasets/{kb_id}")
    
    async def update_knowledge_base(self, kb_id: str, kb_data: KnowledgeBaseUpdate) -> dict:
        """
        更新知识库 - 直接代理RAGFlow

        :param kb_id: 知识库ID
        :param kb_data: 更新数据
        :return: RAGFlow响应数据
        """
        data = kb_data.model_dump(exclude_unset=True)
        return await self._make_kb_request("PUT", f"/api/v1/datasets/{kb_id}", json=data)
    
    async def delete_knowledge_bases(self, kb_ids: List[str]) -> dict:
        """
        删除知识库 - 直接代理RAGFlow

        :param kb_ids: 知识库ID列表
        :return: RAGFlow响应数据
        """
        data = {"ids": kb_ids}
        return await self._make_kb_request("DELETE", "/api/v1/datasets", json=data)
    
    async def list_knowledge_bases(self, query: KnowledgeBaseQuery) -> dict:
        """
        获取知识库列表 - 直接代理RAGFlow

        :param query: 查询参数
        :return: RAGFlow响应数据
        """
        params = query.model_dump(exclude_none=True)
        return await self._make_kb_request("GET", "/api/v1/datasets", params=params)
    
    async def get_knowledge_base_stats(self) -> dict:
        """
        获取知识库统计信息 - 直接从RAGFlow获取

        :return: 统计信息
        """
        try:
            # 获取知识库列表进行统计
            query = KnowledgeBaseQuery(page=1, page_size=100)
            response = await self.list_knowledge_bases(query)

            kb_list = response.get("data", [])

            return {
                "total_knowledge_bases": len(kb_list),
                "total_documents": sum(kb.get("document_count", 0) for kb in kb_list),
                "total_chunks": sum(kb.get("chunk_count", 0) for kb in kb_list),
                "total_tokens": sum(kb.get("token_num", 0) for kb in kb_list),
                "embedding_models": list(set(kb.get("embedding_model") for kb in kb_list if kb.get("embedding_model"))),
                "chunk_methods": list(set(kb.get("chunk_method") for kb in kb_list if kb.get("chunk_method")))
            }

        except Exception:
            return {
                "total_knowledge_bases": 0,
                "total_documents": 0,
                "total_chunks": 0,
                "total_tokens": 0,
                "embedding_models": [],
                "chunk_methods": []
            }

    # 嵌入模型使用固定配置：bge-m3:latest@Ollama
    # 不再提供动态模型选择功能，简化系统复杂度

    # ❌ 已删除所有不必要的方法：
    # - _cache_knowledge_base() - 不必要的缓存
    # - _get_cached_knowledge_bases() - 缓存查询逻辑
    # - _is_name_exists() - 复杂验证逻辑
    # - _check_permission() - 重复权限检查
    # - _update_stats_on_create() - 不必要的统计更新
    # - _update_stats_on_delete() - 不必要的统计更新


# 创建服务实例
knowledge_base_service = KnowledgeBaseService()
